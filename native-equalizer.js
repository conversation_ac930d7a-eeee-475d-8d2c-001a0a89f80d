// WaveCraft - Égaliseur Audio Natif JavaScript

class NativeAudioEqualizer {
    constructor() {
        this.bands = {
            bass: 0,
            low_mid: 0,
            mid: 0,
            high_mid: 0,
            treble: 0
        };
        this.isEnabled = true;
        this.isAudioRunning = false;
        this.currentPreset = 'Flat';
        this.platform = 'Windows';
        this.apiBaseUrl = '/api/equalizer';
        this.isConnected = false;
        
        this.init();
    }

    async init() {
        this.setupElements();
        this.setupEventListeners();
        await this.connectToNativeSystem();
        this.startPeriodicUpdate();
    }

    setupElements() {
        this.elements = {
            // Contrôles principaux
            powerBtn: document.getElementById('powerBtn'),
            powerIcon: document.getElementById('powerIcon'),
            powerText: document.getElementById('powerText'),
            currentPreset: document.getElementById('currentPreset'),
            
            // Contrôles audio
            startAudioBtn: document.getElementById('startAudioBtn'),
            stopAudioBtn: document.getElementById('stopAudioBtn'),
            audioStatus: document.getElementById('audioStatus'),
            
            // Sliders des bandes
            bassSlider: document.getElementById('bassSlider'),
            lowMidSlider: document.getElementById('lowMidSlider'),
            midSlider: document.getElementById('midSlider'),
            highMidSlider: document.getElementById('highMidSlider'),
            trebleSlider: document.getElementById('trebleSlider'),
            
            // Valeurs des bandes
            bassValue: document.getElementById('bassValue'),
            lowMidValue: document.getElementById('lowMidValue'),
            midValue: document.getElementById('midValue'),
            highMidValue: document.getElementById('highMidValue'),
            trebleValue: document.getElementById('trebleValue'),
            
            // Presets
            presetButtons: document.querySelectorAll('.preset-btn'),
            
            // Informations
            statusText: document.getElementById('statusText'),
            statusIndicator: document.getElementById('statusIndicator'),
            eqState: document.getElementById('eqState'),
            currentPresetInfo: document.getElementById('currentPresetInfo'),
            apiStatus: document.getElementById('apiStatus'),
            
            // Boutons de test
            refreshBtn: document.getElementById('refreshBtn'),
            resetBtn: document.getElementById('resetBtn'),
            testEqBtn: document.getElementById('testEqBtn')
        };
    }

    setupEventListeners() {
        // Boutons de contrôle audio
        this.elements.startAudioBtn.addEventListener('click', () => this.startAudioProcessing());
        this.elements.stopAudioBtn.addEventListener('click', () => this.stopAudioProcessing());
        
        // Bouton power
        this.elements.powerBtn.addEventListener('click', () => this.toggleEqualizer());
        
        // Sliders des bandes avec feedback immédiat
        this.elements.bassSlider.addEventListener('input', (e) => {
            this.setBandNative('bass', parseFloat(e.target.value));
        });
        
        this.elements.lowMidSlider.addEventListener('input', (e) => {
            this.setBandNative('low_mid', parseFloat(e.target.value));
        });
        
        this.elements.midSlider.addEventListener('input', (e) => {
            this.setBandNative('mid', parseFloat(e.target.value));
        });
        
        this.elements.highMidSlider.addEventListener('input', (e) => {
            this.setBandNative('high_mid', parseFloat(e.target.value));
        });
        
        this.elements.trebleSlider.addEventListener('input', (e) => {
            this.setBandNative('treble', parseFloat(e.target.value));
        });
        
        // Boutons de presets
        this.elements.presetButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const preset = e.target.dataset.preset;
                this.applyPresetNative(preset);
            });
        });
        
        // Boutons de test
        this.elements.refreshBtn.addEventListener('click', () => this.refreshSystemInfo());
        this.elements.resetBtn.addEventListener('click', () => this.resetEqualizer());
        this.elements.testEqBtn.addEventListener('click', () => this.testNativeEqualizer());
        
        // Raccourcis clavier
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case ' ':
                    if (e.target.tagName !== 'INPUT') {
                        e.preventDefault();
                        if (this.isAudioRunning) {
                            this.stopAudioProcessing();
                        } else {
                            this.startAudioProcessing();
                        }
                    }
                    break;
                case 'r':
                case 'R':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        this.refreshSystemInfo();
                    }
                    break;
                case 'Escape':
                    this.resetEqualizer();
                    break;
                case 't':
                case 'T':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        this.testNativeEqualizer();
                    }
                    break;
            }
        });
    }

    async connectToNativeSystem() {
        try {
            this.setStatus('Connexion à l\'égaliseur natif...', 'info');
            const response = await this.apiCall('get');
            
            if (response.success) {
                this.updateFromSettings(response.settings);
                this.isConnected = true;
                this.setStatus('Connecté à l\'égaliseur natif', 'success');
                this.elements.apiStatus.textContent = 'Connecté NATIF';
                
            } else {
                throw new Error('Échec de connexion égaliseur natif');
            }
        } catch (error) {
            console.error('Erreur connexion égaliseur natif:', error);
            this.setStatus('Erreur connexion égaliseur natif', 'error');
            this.elements.apiStatus.textContent = 'Déconnecté';
            this.isConnected = false;
        }
    }

    async apiCall(action, params = {}) {
        const url = new URL(this.apiBaseUrl, window.location.origin);
        url.searchParams.append('action', action);
        
        Object.keys(params).forEach(key => {
            url.searchParams.append(key, params[key]);
        });

        const response = await fetch(url);
        return await response.json();
    }

    async startAudioProcessing() {
        try {
            this.setStatus('Démarrage du traitement audio natif...', 'info');
            const response = await this.apiCall('start_processing');
            
            if (response.success) {
                this.isAudioRunning = true;
                this.updateAudioControls();
                this.setStatus('✅ Traitement audio natif démarré', 'success');
            } else {
                this.setStatus('❌ Erreur démarrage audio natif', 'error');
            }
        } catch (error) {
            console.error('Erreur démarrage audio:', error);
            this.setStatus('❌ Erreur communication audio', 'error');
        }
    }

    async stopAudioProcessing() {
        try {
            this.isAudioRunning = false;
            this.updateAudioControls();
            this.setStatus('🛑 Traitement audio natif arrêté', 'warning');
        } catch (error) {
            console.error('Erreur arrêt audio:', error);
        }
    }

    updateAudioControls() {
        if (this.isAudioRunning) {
            this.elements.startAudioBtn.disabled = true;
            this.elements.stopAudioBtn.disabled = false;
            this.elements.audioStatus.classList.add('active');
            this.elements.audioStatus.querySelector('.status-text').textContent = 'Audio actif';
        } else {
            this.elements.startAudioBtn.disabled = false;
            this.elements.stopAudioBtn.disabled = true;
            this.elements.audioStatus.classList.remove('active');
            this.elements.audioStatus.querySelector('.status-text').textContent = 'Audio arrêté';
        }
    }

    async setBandNative(band, gain) {
        gain = Math.max(-12, Math.min(12, gain));
        
        // Mise à jour immédiate de l'affichage
        this.updateBandDisplay(band, gain);
        
        try {
            const response = await this.apiCall('set_band', { band, gain });
            
            if (response.success) {
                this.bands[band] = gain;
                this.setStatus(`NATIF: ${this.getBandName(band)} ${gain > 0 ? '+' : ''}${gain} dB`, 'success');
                this.currentPreset = 'Personnalisé';
                this.updatePresetDisplay();
            } else {
                this.setStatus('Erreur réglage bande natif', 'error');
                // Restaurer la valeur précédente en cas d'erreur
                this.updateBandDisplay(band, this.bands[band]);
            }
        } catch (error) {
            console.error('Erreur API setBandNative:', error);
            this.setStatus('Erreur communication égaliseur natif', 'error');
            // Restaurer la valeur précédente en cas d'erreur
            this.updateBandDisplay(band, this.bands[band]);
        }
    }

    async applyPresetNative(presetName) {
        try {
            this.setStatus(`Application preset natif: ${presetName}...`, 'info');
            const response = await this.apiCall('apply_preset', { preset: presetName });
            
            if (response.success) {
                this.updateFromSettings(response.settings);
                this.setStatus(`✅ Preset NATIF "${presetName}" appliqué`, 'success');
            } else {
                this.setStatus('Erreur application preset natif', 'error');
            }
        } catch (error) {
            console.error('Erreur application preset natif:', error);
            this.setStatus('Erreur communication preset natif', 'error');
        }
    }

    async toggleEqualizer() {
        try {
            const response = await this.apiCall('toggle');
            
            if (response.success) {
                this.updateFromSettings(response.settings);
                this.setStatus(this.isEnabled ? 'Égaliseur natif activé' : 'Égaliseur natif désactivé', 'success');
            }
        } catch (error) {
            console.error('Erreur toggle égaliseur natif:', error);
            this.setStatus('Erreur activation/désactivation natif', 'error');
        }
    }

    async resetEqualizer() {
        this.setStatus('Reset égaliseur natif...', 'info');
        await this.applyPresetNative('Flat');
    }

    async testNativeEqualizer() {
        if (!this.isAudioRunning) {
            this.setStatus('⚠️ Démarrez d\'abord le traitement audio', 'warning');
            return;
        }
        
        this.setStatus('🧪 Test NATIF de l\'égaliseur...', 'info');
        
        const testSequence = [
            { preset: 'Bass_Boost', duration: 3000, description: 'Test Basses Natif' },
            { preset: 'Treble_Boost', duration: 3000, description: 'Test Aigus Natif' },
            { preset: 'Vocal', duration: 3000, description: 'Test Voix Natif' },
            { preset: 'Rock', duration: 3000, description: 'Test Rock Natif' },
            { preset: 'Flat', duration: 1000, description: 'Retour Normal' }
        ];
        
        try {
            for (let i = 0; i < testSequence.length; i++) {
                const { preset, duration, description } = testSequence[i];
                
                this.setStatus(`🧪 Test NATIF ${i + 1}/${testSequence.length}: ${description}`, 'info');
                await this.applyPresetNative(preset);
                
                // Attendre pour que l'utilisateur entende la différence
                await new Promise(resolve => setTimeout(resolve, duration));
            }
            
            this.setStatus('✅ Test NATIF terminé - Égaliseur testé', 'success');
        } catch (error) {
            console.error('Erreur test égaliseur natif:', error);
            this.setStatus('❌ Erreur pendant le test NATIF', 'error');
            // Restaurer le preset Flat en cas d'erreur
            await this.applyPresetNative('Flat');
        }
    }

    async refreshSystemInfo() {
        this.setStatus('Actualisation égaliseur natif...', 'info');
        await this.connectToNativeSystem();
    }

    updateFromSettings(settings) {
        this.bands = { ...settings.bands };
        this.isEnabled = settings.enabled;
        this.currentPreset = settings.preset;
        this.platform = settings.platform;
        
        this.updateAllDisplays();
    }

    updateAllDisplays() {
        // Mettre à jour les sliders et valeurs
        Object.keys(this.bands).forEach(band => {
            this.updateBandDisplay(band, this.bands[band]);
        });
        
        // Mettre à jour l'état power
        this.updatePowerDisplay();
        
        // Mettre à jour les presets
        this.updatePresetDisplay();
        
        // Mettre à jour les informations
        this.updateInfoDisplay();
    }

    updateBandDisplay(band, gain) {
        const sliderMap = {
            bass: this.elements.bassSlider,
            low_mid: this.elements.lowMidSlider,
            mid: this.elements.midSlider,
            high_mid: this.elements.highMidSlider,
            treble: this.elements.trebleSlider
        };
        
        const valueMap = {
            bass: this.elements.bassValue,
            low_mid: this.elements.lowMidValue,
            mid: this.elements.midValue,
            high_mid: this.elements.highMidValue,
            treble: this.elements.trebleValue
        };
        
        if (sliderMap[band]) {
            sliderMap[band].value = gain;
        }
        
        if (valueMap[band]) {
            const displayValue = `${gain > 0 ? '+' : ''}${gain} dB`;
            valueMap[band].textContent = displayValue;
            
            // Colorer selon le gain (style natif)
            if (gain > 0) {
                valueMap[band].style.color = '#2196F3';
                valueMap[band].style.fontWeight = 'bold';
            } else if (gain < 0) {
                valueMap[band].style.color = '#FF5722';
                valueMap[band].style.fontWeight = 'bold';
            } else {
                valueMap[band].style.color = '#333';
                valueMap[band].style.fontWeight = 'normal';
            }
        }
    }

    updatePowerDisplay() {
        if (this.isEnabled) {
            this.elements.powerBtn.classList.remove('off');
            this.elements.powerIcon.textContent = '⚡';
            this.elements.powerText.textContent = 'NATIF';
        } else {
            this.elements.powerBtn.classList.add('off');
            this.elements.powerIcon.textContent = '⏸️';
            this.elements.powerText.textContent = 'OFF';
        }
    }

    updatePresetDisplay() {
        this.elements.currentPreset.textContent = this.currentPreset;
        this.elements.currentPresetInfo.textContent = this.currentPreset;
        
        // Mettre à jour les boutons de preset
        this.elements.presetButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.preset === this.currentPreset) {
                btn.classList.add('active');
            }
        });
    }

    updateInfoDisplay() {
        this.elements.eqState.textContent = this.isEnabled ? 'Activé NATIF' : 'Désactivé';
    }

    getBandName(band) {
        const names = {
            bass: 'Basses',
            low_mid: 'Médiums-Bas',
            mid: 'Médiums',
            high_mid: 'Médiums-Hauts',
            treble: 'Aigus'
        };
        return names[band] || band;
    }

    setStatus(message, type = 'info') {
        this.elements.statusText.textContent = message;
        
        const statusDot = this.elements.statusIndicator.querySelector('.status-dot');
        statusDot.className = 'status-dot';
        
        switch(type) {
            case 'success':
                statusDot.style.background = '#2196F3';
                break;
            case 'warning':
                statusDot.style.background = '#FF9800';
                break;
            case 'error':
                statusDot.style.background = '#F44336';
                break;
            default:
                statusDot.style.background = '#2196F3';
        }
        
        // Effacer le message après 4 secondes
        if (type !== 'error') {
            setTimeout(() => {
                this.elements.statusText.textContent = 'Égaliseur NATIF prêt';
                statusDot.style.background = '#2196F3';
            }, 4000);
        }
    }

    startPeriodicUpdate() {
        // Actualiser les informations toutes les 10 secondes
        setInterval(async () => {
            if (this.isConnected) {
                try {
                    const response = await this.apiCall('get');
                    if (response.success) {
                        // Mettre à jour silencieusement si les réglages ont changé
                        const currentSettings = JSON.stringify(this.bands);
                        const newSettings = JSON.stringify(response.settings.bands);
                        
                        if (currentSettings !== newSettings || 
                            this.isEnabled !== response.settings.enabled) {
                            this.updateFromSettings(response.settings);
                        }
                    }
                } catch (error) {
                    // Ignorer les erreurs de mise à jour périodique
                }
            }
        }, 10000);
    }
}

// Initialiser l'égaliseur NATIF quand la page est chargée
document.addEventListener('DOMContentLoaded', () => {
    window.nativeAudioEqualizer = new NativeAudioEqualizer();
    
    // Message d'accueil pour l'égaliseur NATIF
    console.log('🎚️ WaveCraft - Égaliseur Audio NATIF initialisé');
    console.log('🔧 Égaliseur codé depuis zéro sans dépendances externes');
    console.log('🎵 Cliquez sur "Démarrer Audio" pour activer le traitement');
});

// Gestion de la visibilité de la page
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.nativeAudioEqualizer) {
        window.nativeAudioEqualizer.refreshSystemInfo();
    }
});
