#!/usr/bin/env python3
"""
WaveCraft - Contrôle de Volume Système
Application native pour contrôler réellement le volume du PC/téléphone
"""

import sys
import json
import time
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import webbrowser

# Imports spécifiques à la plateforme
try:
    if sys.platform == "win32":
        # Windows - Contrôle volume via pycaw
        from pycaw.pycaw import AudioUtilities, IAudioEndpointVolume
        from comtypes import CLSCTX_ALL
        from ctypes import cast, POINTER
        import ctypes
        PLATFORM = "Windows"
    elif sys.platform == "darwin":
        # macOS - Contrôle volume via osascript
        import subprocess
        PLATFORM = "macOS"
    elif sys.platform.startswith("linux"):
        # Linux - Contrôle volume via ALSA/PulseAudio
        import subprocess
        PLATFORM = "Linux"
    else:
        PLATFORM = "Inconnu"
        print("Plateforme non supportée")
except ImportError as e:
    print(f"Dépendances manquantes: {e}")
    print("Installez: pip install pycaw comtypes")
    PLATFORM = "Simulation"

class SystemVolumeController:
    """Contrôleur de volume système multi-plateforme"""
    
    def __init__(self):
        self.current_volume = 50
        self.is_muted = False
        self.platform = PLATFORM
        self.audio_interface = None
        self.initialize_audio_interface()
    
    def initialize_audio_interface(self):
        """Initialise l'interface audio selon la plateforme"""
        try:
            if self.platform == "Windows":
                self._init_windows_audio()
            elif self.platform == "macOS":
                self._init_macos_audio()
            elif self.platform == "Linux":
                self._init_linux_audio()
            else:
                print("Mode simulation - pas de contrôle système réel")
        except Exception as e:
            print(f"Erreur initialisation audio: {e}")
            self.platform = "Simulation"
    
    def _init_windows_audio(self):
        """Initialise l'interface audio Windows"""
        devices = AudioUtilities.GetSpeakers()
        interface = devices.Activate(IAudioEndpointVolume._iid_, CLSCTX_ALL, None)
        self.audio_interface = cast(interface, POINTER(IAudioEndpointVolume))
        
        # Récupérer le volume actuel
        current_vol = self.audio_interface.GetMasterScalarVolume()
        self.current_volume = int(current_vol * 100)
        self.is_muted = self.audio_interface.GetMute()
        
        print(f"Audio Windows initialisé - Volume: {self.current_volume}%")
    
    def _init_macos_audio(self):
        """Initialise l'interface audio macOS"""
        try:
            # Récupérer le volume actuel
            result = subprocess.run(['osascript', '-e', 'output volume of (get volume settings)'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.current_volume = int(result.stdout.strip())
                print(f"Audio macOS initialisé - Volume: {self.current_volume}%")
        except Exception as e:
            print(f"Erreur macOS audio: {e}")
    
    def _init_linux_audio(self):
        """Initialise l'interface audio Linux"""
        try:
            # Tenter PulseAudio d'abord
            result = subprocess.run(['pactl', 'get-sink-volume', '@DEFAULT_SINK@'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                # Parser la sortie PulseAudio
                output = result.stdout
                # Extraire le pourcentage (format: "Volume: front-left: 32768 /  50% ...")
                import re
                match = re.search(r'(\d+)%', output)
                if match:
                    self.current_volume = int(match.group(1))
                    print(f"Audio Linux (PulseAudio) initialisé - Volume: {self.current_volume}%")
                    return
            
            # Fallback vers ALSA
            result = subprocess.run(['amixer', 'get', 'Master'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                import re
                match = re.search(r'\[(\d+)%\]', result.stdout)
                if match:
                    self.current_volume = int(match.group(1))
                    print(f"Audio Linux (ALSA) initialisé - Volume: {self.current_volume}%")
        except Exception as e:
            print(f"Erreur Linux audio: {e}")
    
    def set_volume(self, volume):
        """Définit le volume système (0-100)"""
        volume = max(0, min(100, volume))
        
        try:
            if self.platform == "Windows" and self.audio_interface:
                self.audio_interface.SetMasterScalarVolume(volume / 100.0, None)
                
            elif self.platform == "macOS":
                subprocess.run(['osascript', '-e', f'set volume output volume {volume}'])
                
            elif self.platform == "Linux":
                # Tenter PulseAudio
                try:
                    subprocess.run(['pactl', 'set-sink-volume', '@DEFAULT_SINK@', f'{volume}%'])
                except:
                    # Fallback ALSA
                    subprocess.run(['amixer', 'set', 'Master', f'{volume}%'])
            
            self.current_volume = volume
            print(f"Volume système défini à {volume}%")
            return True
            
        except Exception as e:
            print(f"Erreur définition volume: {e}")
            return False
    
    def get_volume(self):
        """Récupère le volume système actuel"""
        try:
            if self.platform == "Windows" and self.audio_interface:
                vol = self.audio_interface.GetMasterScalarVolume()
                self.current_volume = int(vol * 100)
                
            elif self.platform == "macOS":
                result = subprocess.run(['osascript', '-e', 'output volume of (get volume settings)'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    self.current_volume = int(result.stdout.strip())
                    
            elif self.platform == "Linux":
                try:
                    result = subprocess.run(['pactl', 'get-sink-volume', '@DEFAULT_SINK@'], 
                                          capture_output=True, text=True)
                    if result.returncode == 0:
                        import re
                        match = re.search(r'(\d+)%', result.stdout)
                        if match:
                            self.current_volume = int(match.group(1))
                except:
                    result = subprocess.run(['amixer', 'get', 'Master'], 
                                          capture_output=True, text=True)
                    if result.returncode == 0:
                        import re
                        match = re.search(r'\[(\d+)%\]', result.stdout)
                        if match:
                            self.current_volume = int(match.group(1))
            
            return self.current_volume
            
        except Exception as e:
            print(f"Erreur récupération volume: {e}")
            return self.current_volume
    
    def mute(self):
        """Active/désactive le mode muet"""
        try:
            if self.platform == "Windows" and self.audio_interface:
                self.audio_interface.SetMute(not self.is_muted, None)
                self.is_muted = not self.is_muted
                
            elif self.platform == "macOS":
                if self.is_muted:
                    subprocess.run(['osascript', '-e', f'set volume output volume {self.current_volume}'])
                else:
                    subprocess.run(['osascript', '-e', 'set volume with output muted'])
                self.is_muted = not self.is_muted
                
            elif self.platform == "Linux":
                try:
                    subprocess.run(['pactl', 'set-sink-mute', '@DEFAULT_SINK@', 'toggle'])
                except:
                    subprocess.run(['amixer', 'set', 'Master', 'toggle'])
                self.is_muted = not self.is_muted
            
            print(f"Muet {'activé' if self.is_muted else 'désactivé'}")
            return True
            
        except Exception as e:
            print(f"Erreur muet: {e}")
            return False
    
    def increase_volume(self, step=5):
        """Augmente le volume"""
        new_volume = min(100, self.current_volume + step)
        return self.set_volume(new_volume)
    
    def decrease_volume(self, step=5):
        """Diminue le volume"""
        new_volume = max(0, self.current_volume - step)
        return self.set_volume(new_volume)

# Instance globale du contrôleur
volume_controller = SystemVolumeController()

class VolumeHTTPHandler(BaseHTTPRequestHandler):
    """Serveur HTTP pour l'interface web"""
    
    def do_GET(self):
        """Gère les requêtes GET"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.serve_main_page()
        elif parsed_path.path == '/api/volume':
            self.handle_volume_api(parsed_path.query)
        elif parsed_path.path.endswith('.css'):
            self.serve_static_file('styles.css', 'text/css')
        elif parsed_path.path.endswith('.js'):
            self.serve_static_file('system-volume.js', 'application/javascript')
        else:
            self.send_error(404)
    
    def serve_main_page(self):
        """Sert la page principale"""
        try:
            with open('system-volume.html', 'r', encoding='utf-8') as f:
                content = f.read()
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, "Page non trouvée")
    
    def serve_static_file(self, filename, content_type):
        """Sert les fichiers statiques"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            self.send_response(200)
            self.send_header('Content-type', content_type)
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404)
    
    def handle_volume_api(self, query):
        """Gère l'API de contrôle du volume"""
        params = parse_qs(query)
        action = params.get('action', [''])[0]
        
        response_data = {
            'success': False,
            'volume': volume_controller.get_volume(),
            'muted': volume_controller.is_muted,
            'platform': volume_controller.platform
        }
        
        if action == 'get':
            response_data['success'] = True
            
        elif action == 'set':
            volume = int(params.get('volume', [50])[0])
            response_data['success'] = volume_controller.set_volume(volume)
            response_data['volume'] = volume_controller.get_volume()
            
        elif action == 'increase':
            response_data['success'] = volume_controller.increase_volume()
            response_data['volume'] = volume_controller.get_volume()
            
        elif action == 'decrease':
            response_data['success'] = volume_controller.decrease_volume()
            response_data['volume'] = volume_controller.get_volume()
            
        elif action == 'mute':
            response_data['success'] = volume_controller.mute()
            response_data['muted'] = volume_controller.is_muted
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response_data).encode('utf-8'))
    
    def log_message(self, format, *args):
        """Supprime les logs HTTP pour plus de clarté"""
        pass

def start_server():
    """Démarre le serveur HTTP"""
    server_address = ('localhost', 8080)
    httpd = HTTPServer(server_address, VolumeHTTPHandler)
    
    print(f"🎵 WaveCraft - Contrôle Volume Système")
    print(f"📱 Plateforme: {PLATFORM}")
    print(f"🌐 Serveur démarré sur http://localhost:8080")
    print(f"🔊 Volume actuel: {volume_controller.get_volume()}%")
    print(f"🔇 Muet: {'Oui' if volume_controller.is_muted else 'Non'}")
    print("\n💡 Ouvrez votre navigateur sur http://localhost:8080")
    print("⏹️  Appuyez sur Ctrl+C pour arrêter\n")
    
    # Ouvrir automatiquement le navigateur
    threading.Timer(1.0, lambda: webbrowser.open('http://localhost:8080')).start()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur...")
        httpd.shutdown()

if __name__ == "__main__":
    start_server()
