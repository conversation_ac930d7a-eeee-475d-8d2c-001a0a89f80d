<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WaveCraft - Contrôle de Volume</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎵 WaveCraft</h1>
            <p>Contrôle de Volume Uniforme</p>
        </header>

        <main class="volume-control">
            <div class="volume-display">
                <div class="volume-meter">
                    <div class="volume-bar" id="volumeBar"></div>
                </div>
                <span class="volume-percentage" id="volumePercentage">50%</span>
            </div>

            <div class="controls">
                <button class="control-btn decrease" id="decreaseBtn">
                    <span class="icon">🔉</span>
                    <span class="text">Diminuer</span>
                </button>

                <button class="control-btn mute" id="muteBtn">
                    <span class="icon" id="muteIcon">🔊</span>
                    <span class="text">Muet</span>
                </button>

                <button class="control-btn increase" id="increaseBtn">
                    <span class="icon">🔊</span>
                    <span class="text">Augmenter</span>
                </button>
            </div>

            <div class="test-controls">
                <button class="test-btn" id="testAudioBtn">
                    <span class="icon">🎵</span>
                    <span class="text">Test Audio</span>
                </button>
                <button class="test-btn" id="playToneBtn">
                    <span class="icon">🔔</span>
                    <span class="text">Jouer Son</span>
                </button>
            </div>

            <div class="volume-slider-container">
                <input type="range"
                       class="volume-slider"
                       id="volumeSlider"
                       min="0"
                       max="100"
                       value="50">
            </div>

            <div class="status">
                <div class="status-indicator" id="statusIndicator">
                    <span class="status-dot"></span>
                    <span class="status-text" id="statusText">Prêt</span>
                </div>
            </div>

            <div class="info-panel">
                <h3>📊 Informations</h3>
                <div class="info-item">
                    <span class="label">Volume actuel :</span>
                    <span class="value" id="currentVolume">50%</span>
                </div>
                <div class="info-item">
                    <span class="label">État :</span>
                    <span class="value" id="audioState">Actif</span>
                </div>
                <div class="info-item">
                    <span class="label">Plateforme :</span>
                    <span class="value" id="platform">Détection...</span>
                </div>
            </div>
        </main>

        <footer>
            <p>WaveCraft v1.0 - Test de Contrôle de Volume</p>
        </footer>
    </div>

    <script src="volume-control.js"></script>
</body>
</html>
