@echo off
echo ========================================
echo WaveCraft - Installation des dependances
echo ========================================
echo.

echo Installation de Python et des dependances...
echo.

REM Verifier si Python est installe
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Python n'est pas installe ou pas dans le PATH
    echo Veuillez installer Python depuis https://python.org
    pause
    exit /b 1
)

echo Python detecte avec succes!
echo.

echo Installation des dependances Python...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo.
    echo ERREUR: Echec de l'installation des dependances
    echo Essayez manuellement: pip install pycaw comtypes
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installation terminee avec succes!
echo ========================================
echo.
echo Pour demarrer WaveCraft:
echo   python system-volume-control.py
echo.
echo Ou double-cliquez sur start.bat
echo.
pause
