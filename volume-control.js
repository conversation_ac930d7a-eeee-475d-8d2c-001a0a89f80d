// WaveCraft - Contrôle de Volume JavaScript

class VolumeController {
    constructor() {
        this.currentVolume = 50;
        this.isMuted = false;
        this.previousVolume = 50;
        this.audioContext = null;
        this.gainNode = null;
        this.isInitialized = false;
        
        this.init();
    }

    async init() {
        this.setupElements();
        this.setupEventListeners();
        this.detectPlatform();
        await this.initializeAudioContext();
        this.updateDisplay();
        this.setStatus('Prêt', 'success');
    }

    setupElements() {
        this.elements = {
            volumeBar: document.getElementById('volumeBar'),
            volumePercentage: document.getElementById('volumePercentage'),
            volumeSlider: document.getElementById('volumeSlider'),
            decreaseBtn: document.getElementById('decreaseBtn'),
            increaseBtn: document.getElementById('increaseBtn'),
            muteBtn: document.getElementById('muteBtn'),
            muteIcon: document.getElementById('muteIcon'),
            statusText: document.getElementById('statusText'),
            statusIndicator: document.getElementById('statusIndicator'),
            currentVolume: document.getElementById('currentVolume'),
            audioState: document.getElementById('audioState'),
            platform: document.getElementById('platform')
        };
    }

    setupEventListeners() {
        // Boutons de contrôle
        this.elements.decreaseBtn.addEventListener('click', () => this.decreaseVolume());
        this.elements.increaseBtn.addEventListener('click', () => this.increaseVolume());
        this.elements.muteBtn.addEventListener('click', () => this.toggleMute());
        
        // Slider de volume
        this.elements.volumeSlider.addEventListener('input', (e) => {
            this.setVolume(parseInt(e.target.value));
        });

        // Raccourcis clavier
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowUp':
                    e.preventDefault();
                    this.increaseVolume();
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.decreaseVolume();
                    break;
                case ' ':
                    e.preventDefault();
                    this.toggleMute();
                    break;
            }
        });
    }

    async initializeAudioContext() {
        try {
            // Initialiser le contexte audio
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Créer un nœud de gain pour contrôler le volume
            this.gainNode = this.audioContext.createGain();
            this.gainNode.connect(this.audioContext.destination);
            
            // Définir le volume initial
            this.gainNode.gain.value = this.currentVolume / 100;
            
            this.isInitialized = true;
            this.setStatus('Audio initialisé', 'success');
            
        } catch (error) {
            console.warn('Impossible d\'initialiser le contexte audio:', error);
            this.setStatus('Mode simulation', 'warning');
        }
    }

    setVolume(volume) {
        // Limiter le volume entre 0 et 100
        volume = Math.max(0, Math.min(100, volume));
        
        this.currentVolume = volume;
        this.isMuted = volume === 0;
        
        // Appliquer le volume au contexte audio si disponible
        if (this.isInitialized && this.gainNode) {
            this.gainNode.gain.value = volume / 100;
        }
        
        // Tenter d'utiliser l'API Media Session si disponible
        if ('mediaSession' in navigator) {
            try {
                // Note: L'API Media Session ne permet pas de contrôler directement le volume système
                // mais peut être utilisée pour les notifications de contrôle média
                navigator.mediaSession.setActionHandler('volumechange', () => {
                    this.updateSystemVolume(volume);
                });
            } catch (error) {
                console.log('Media Session API non supportée pour le volume');
            }
        }
        
        this.updateDisplay();
        this.logVolumeChange(volume);
    }

    increaseVolume() {
        const newVolume = Math.min(100, this.currentVolume + 5);
        this.setVolume(newVolume);
        this.setStatus('Volume augmenté', 'success');
    }

    decreaseVolume() {
        const newVolume = Math.max(0, this.currentVolume - 5);
        this.setVolume(newVolume);
        this.setStatus('Volume diminué', 'success');
    }

    toggleMute() {
        if (this.isMuted) {
            this.setVolume(this.previousVolume);
            this.setStatus('Son rétabli', 'success');
        } else {
            this.previousVolume = this.currentVolume;
            this.setVolume(0);
            this.setStatus('Son coupé', 'warning');
        }
    }

    updateDisplay() {
        // Mettre à jour la barre de volume
        this.elements.volumeBar.style.width = `${this.currentVolume}%`;
        
        // Mettre à jour le pourcentage
        this.elements.volumePercentage.textContent = `${this.currentVolume}%`;
        this.elements.currentVolume.textContent = `${this.currentVolume}%`;
        
        // Mettre à jour le slider
        this.elements.volumeSlider.value = this.currentVolume;
        
        // Mettre à jour l'icône de muet
        if (this.isMuted || this.currentVolume === 0) {
            this.elements.muteIcon.textContent = '🔇';
            this.elements.audioState.textContent = 'Muet';
        } else if (this.currentVolume < 30) {
            this.elements.muteIcon.textContent = '🔉';
            this.elements.audioState.textContent = 'Faible';
        } else if (this.currentVolume < 70) {
            this.elements.muteIcon.textContent = '🔊';
            this.elements.audioState.textContent = 'Moyen';
        } else {
            this.elements.muteIcon.textContent = '🔊';
            this.elements.audioState.textContent = 'Fort';
        }
    }

    updateSystemVolume(volume) {
        // Tentative de contrôle du volume système (limité dans les navigateurs)
        // Cette fonction simule le contrôle système
        console.log(`Tentative de réglage du volume système à ${volume}%`);
        
        // Pour un vrai contrôle système, il faudrait :
        // 1. Une extension de navigateur
        // 2. Une application native
        // 3. Des APIs spécifiques à la plateforme
    }

    detectPlatform() {
        const userAgent = navigator.userAgent;
        let platform = 'Inconnu';
        
        if (/Windows/i.test(userAgent)) {
            platform = 'Windows';
        } else if (/Mac/i.test(userAgent)) {
            platform = 'macOS';
        } else if (/Linux/i.test(userAgent)) {
            platform = 'Linux';
        } else if (/Android/i.test(userAgent)) {
            platform = 'Android';
        } else if (/iPhone|iPad/i.test(userAgent)) {
            platform = 'iOS';
        }
        
        this.elements.platform.textContent = platform;
    }

    setStatus(message, type = 'info') {
        this.elements.statusText.textContent = message;
        
        const statusDot = this.elements.statusIndicator.querySelector('.status-dot');
        statusDot.className = 'status-dot';
        
        switch(type) {
            case 'success':
                statusDot.style.background = '#4CAF50';
                break;
            case 'warning':
                statusDot.style.background = '#FF9800';
                break;
            case 'error':
                statusDot.style.background = '#F44336';
                break;
            default:
                statusDot.style.background = '#2196F3';
        }
        
        // Effacer le message après 3 secondes
        setTimeout(() => {
            this.elements.statusText.textContent = 'Prêt';
            statusDot.style.background = '#4CAF50';
        }, 3000);
    }

    logVolumeChange(volume) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] Volume changé à ${volume}%`);
    }
}

// Initialiser le contrôleur de volume quand la page est chargée
document.addEventListener('DOMContentLoaded', () => {
    window.volumeController = new VolumeController();
});

// Gestion de la visibilité de la page
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        console.log('Application en arrière-plan');
    } else {
        console.log('Application au premier plan');
        if (window.volumeController) {
            window.volumeController.setStatus('Application active', 'success');
        }
    }
});
