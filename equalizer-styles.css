/* WaveCraft - Styles Égaliseur Audio */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 15px;
}

.platform-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(255,255,255,0.2);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.method-badge {
    background: rgba(76, 175, 80, 0.8);
    padding: 4px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
    color: white;
}

.real-indicator {
    display: flex;
    align-items: center;
}

.real-badge {
    background: linear-gradient(145deg, #4CAF50, #45a049);
    color: white;
    padding: 8px 16px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
    animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
    0% { box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3); }
    50% { box-shadow: 0 6px 20px rgba(76, 175, 80, 0.5); }
    100% { box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3); }
}

.real-control {
    color: #4CAF50 !important;
    font-weight: bold;
}

.real-test {
    background: linear-gradient(145deg, #4CAF50, #45a049) !important;
    color: white !important;
}

.real-test:hover {
    background: linear-gradient(145deg, #45a049, #388e3c) !important;
}

.warning-panel {
    background: #fff3cd;
    border: 2px solid #ffc107;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.warning-panel h3 {
    color: #856404;
    margin-bottom: 10px;
}

.warning-panel p {
    color: #856404;
    margin-bottom: 10px;
    line-height: 1.4;
}

.warning-panel ul {
    color: #856404;
    margin-left: 20px;
}

.warning-panel li {
    margin-bottom: 5px;
}

.equalizer-control {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    flex: 1;
}

.equalizer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
}

.eq-status {
    display: flex;
    align-items: center;
    gap: 20px;
}

.power-btn {
    background: linear-gradient(145deg, #4CAF50, #45a049);
    border: none;
    border-radius: 50px;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.power-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.power-btn.off {
    background: linear-gradient(145deg, #f44336, #d32f2f);
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.power-btn.off:hover {
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

.current-preset {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
}

.preset-label {
    color: #666;
    font-weight: 500;
}

.preset-name {
    color: #333;
    font-weight: bold;
    background: #f8f9fa;
    padding: 5px 12px;
    border-radius: 15px;
}

.equalizer-bands {
    margin-bottom: 30px;
}

.band-container {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    flex-wrap: wrap;
}

.band-control {
    flex: 1;
    min-width: 120px;
    text-align: center;
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px 10px;
    transition: all 0.3s ease;
}

.band-control:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.band-label {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.band-freq {
    color: #666;
    font-size: 0.8rem;
    margin-bottom: 15px;
}

.band-slider-container {
    position: relative;
    margin: 20px 0;
}

.band-slider {
    width: 120px;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    cursor: pointer;
    transform: rotate(-90deg);
    margin: 60px 0;
}

.band-slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.band-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.slider-marks {
    display: flex;
    justify-content: space-between;
    font-size: 0.7rem;
    color: #999;
    margin-top: 5px;
}

.band-value {
    font-weight: bold;
    color: #333;
    font-size: 0.9rem;
    background: white;
    padding: 5px 10px;
    border-radius: 10px;
    border: 2px solid #e0e0e0;
}

.presets-section {
    margin-bottom: 25px;
}

.presets-section h3 {
    margin-bottom: 15px;
    color: #333;
    text-align: center;
}

.preset-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.preset-btn {
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border: none;
    border-radius: 10px;
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    font-weight: 500;
    color: #495057;
    border: 2px solid transparent;
}

.preset-btn:hover {
    background: linear-gradient(145deg, #e9ecef, #dee2e6);
    transform: translateY(-1px);
}

.preset-btn.active {
    background: linear-gradient(145deg, #667eea, #764ba2);
    color: white;
    border-color: #5a6fd8;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.custom-presets {
    margin-bottom: 25px;
}

.custom-presets h3 {
    margin-bottom: 15px;
    color: #333;
    text-align: center;
}

.custom-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-bottom: 15px;
}

#presetName {
    padding: 10px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 0.9rem;
    outline: none;
    transition: border-color 0.3s ease;
}

#presetName:focus {
    border-color: #667eea;
}

.save-btn {
    background: linear-gradient(145deg, #28a745, #20c997);
    border: none;
    border-radius: 10px;
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

.save-btn:hover {
    background: linear-gradient(145deg, #20c997, #17a2b8);
    transform: translateY(-1px);
}

.saved-presets {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
}

.status {
    text-align: center;
    margin-bottom: 20px;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: #f5f5f5;
    border-radius: 20px;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #4CAF50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.info-panel {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.info-panel h3 {
    margin-bottom: 15px;
    color: #333;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.label {
    font-weight: 500;
    color: #666;
}

.value {
    font-weight: bold;
    color: #333;
}

.test-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.test-btn {
    background: linear-gradient(145deg, #e3f2fd, #bbdefb);
    border: none;
    border-radius: 12px;
    padding: 12px 18px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 3px 3px 8px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    font-weight: 500;
}

.test-btn:hover {
    transform: translateY(-1px);
    box-shadow: 5px 5px 12px rgba(0,0,0,0.15);
    background: linear-gradient(145deg, #bbdefb, #90caf9);
}

footer {
    text-align: center;
    color: white;
    opacity: 0.8;
    margin-top: 20px;
}

.api-status {
    font-size: 0.9rem;
    margin-top: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .band-container {
        flex-direction: column;
        gap: 20px;
    }

    .band-control {
        min-width: auto;
    }

    .band-slider {
        transform: none;
        width: 100%;
        margin: 20px 0;
    }

    .equalizer-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .preset-buttons {
        gap: 8px;
    }

    .preset-btn {
        font-size: 0.8rem;
        padding: 8px 12px;
    }
}
