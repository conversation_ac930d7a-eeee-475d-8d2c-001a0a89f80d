<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WaveCraft - Contrôle Volume Système</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎵 WaveCraft</h1>
            <p>Contrôle Volume Système Réel</p>
            <div class="platform-badge" id="platformBadge">
                <span id="platformName">Détection...</span>
            </div>
        </header>

        <main class="volume-control">
            <div class="volume-display">
                <div class="volume-meter">
                    <div class="volume-bar" id="volumeBar"></div>
                </div>
                <span class="volume-percentage" id="volumePercentage">---%</span>
            </div>

            <div class="controls">
                <button class="control-btn decrease" id="decreaseBtn">
                    <span class="icon">🔉</span>
                    <span class="text">-5%</span>
                </button>
                
                <button class="control-btn mute" id="muteBtn">
                    <span class="icon" id="muteIcon">🔊</span>
                    <span class="text">Muet</span>
                </button>
                
                <button class="control-btn increase" id="increaseBtn">
                    <span class="icon">🔊</span>
                    <span class="text">+5%</span>
                </button>
            </div>

            <div class="volume-slider-container">
                <input type="range" 
                       class="volume-slider" 
                       id="volumeSlider" 
                       min="0" 
                       max="100" 
                       value="50">
                <div class="slider-labels">
                    <span>0%</span>
                    <span>50%</span>
                    <span>100%</span>
                </div>
            </div>

            <div class="quick-presets">
                <h3>🎚️ Presets Rapides</h3>
                <div class="preset-buttons">
                    <button class="preset-btn" data-volume="10">Silencieux (10%)</button>
                    <button class="preset-btn" data-volume="30">Faible (30%)</button>
                    <button class="preset-btn" data-volume="50">Normal (50%)</button>
                    <button class="preset-btn" data-volume="75">Fort (75%)</button>
                    <button class="preset-btn" data-volume="100">Maximum (100%)</button>
                </div>
            </div>

            <div class="status">
                <div class="status-indicator" id="statusIndicator">
                    <span class="status-dot"></span>
                    <span class="status-text" id="statusText">Connexion...</span>
                </div>
            </div>

            <div class="info-panel">
                <h3>📊 Informations Système</h3>
                <div class="info-item">
                    <span class="label">Volume actuel :</span>
                    <span class="value" id="currentVolume">---%</span>
                </div>
                <div class="info-item">
                    <span class="label">État :</span>
                    <span class="value" id="audioState">Détection...</span>
                </div>
                <div class="info-item">
                    <span class="label">Plateforme :</span>
                    <span class="value" id="platform">Détection...</span>
                </div>
                <div class="info-item">
                    <span class="label">Contrôle :</span>
                    <span class="value" id="controlType">Système Réel</span>
                </div>
            </div>

            <div class="test-controls">
                <button class="test-btn" id="refreshBtn">
                    <span class="icon">🔄</span>
                    <span class="text">Actualiser</span>
                </button>
                <button class="test-btn" id="testSystemBtn">
                    <span class="icon">🧪</span>
                    <span class="text">Test Système</span>
                </button>
            </div>
        </main>

        <footer>
            <p>WaveCraft v2.0 - Contrôle Volume Système Natif</p>
            <p class="api-status">API: <span id="apiStatus">Connexion...</span></p>
        </footer>
    </div>

    <script src="system-volume.js"></script>
</body>
</html>
