// WaveCraft - Égaliseur Audio JavaScript

class SystemAudioEqualizer {
    constructor() {
        this.bands = {
            bass: 0,
            low_mid: 0,
            mid: 0,
            high_mid: 0,
            treble: 0
        };
        this.isEnabled = true;
        this.currentPreset = 'Flat';
        this.platform = 'Inconnu';
        this.apiBaseUrl = '/api/equalizer';
        this.isConnected = false;
        this.customPresets = {};
        
        this.init();
    }

    async init() {
        this.setupElements();
        this.setupEventListeners();
        await this.connectToSystem();
        this.startPeriodicUpdate();
    }

    setupElements() {
        this.elements = {
            // Contrôles principaux
            powerBtn: document.getElementById('powerBtn'),
            powerIcon: document.getElementById('powerIcon'),
            powerText: document.getElementById('powerText'),
            currentPreset: document.getElementById('currentPreset'),
            
            // Sliders des bandes
            bassSlider: document.getElementById('bassSlider'),
            lowMidSlider: document.getElementById('lowMidSlider'),
            midSlider: document.getElementById('midSlider'),
            highMidSlider: document.getElementById('highMidSlider'),
            trebleSlider: document.getElementById('trebleSlider'),
            
            // Valeurs des bandes
            bassValue: document.getElementById('bassValue'),
            lowMidValue: document.getElementById('lowMidValue'),
            midValue: document.getElementById('midValue'),
            highMidValue: document.getElementById('highMidValue'),
            trebleValue: document.getElementById('trebleValue'),
            
            // Presets
            presetButtons: document.querySelectorAll('.preset-btn'),
            presetName: document.getElementById('presetName'),
            savePresetBtn: document.getElementById('savePresetBtn'),
            savedPresets: document.getElementById('savedPresets'),
            
            // Informations
            statusText: document.getElementById('statusText'),
            statusIndicator: document.getElementById('statusIndicator'),
            eqState: document.getElementById('eqState'),
            currentPresetInfo: document.getElementById('currentPresetInfo'),
            platform: document.getElementById('platform'),
            platformName: document.getElementById('platformName'),
            equalizerType: document.getElementById('equalizerType'),
            apiStatus: document.getElementById('apiStatus'),
            
            // Boutons de test
            refreshBtn: document.getElementById('refreshBtn'),
            resetBtn: document.getElementById('resetBtn'),
            testEqBtn: document.getElementById('testEqBtn')
        };
    }

    setupEventListeners() {
        // Bouton power
        this.elements.powerBtn.addEventListener('click', () => this.toggleEqualizer());
        
        // Sliders des bandes
        this.elements.bassSlider.addEventListener('input', (e) => {
            this.setBand('bass', parseFloat(e.target.value));
        });
        
        this.elements.lowMidSlider.addEventListener('input', (e) => {
            this.setBand('low_mid', parseFloat(e.target.value));
        });
        
        this.elements.midSlider.addEventListener('input', (e) => {
            this.setBand('mid', parseFloat(e.target.value));
        });
        
        this.elements.highMidSlider.addEventListener('input', (e) => {
            this.setBand('high_mid', parseFloat(e.target.value));
        });
        
        this.elements.trebleSlider.addEventListener('input', (e) => {
            this.setBand('treble', parseFloat(e.target.value));
        });
        
        // Boutons de presets
        this.elements.presetButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const preset = e.target.dataset.preset;
                this.applyPreset(preset);
            });
        });
        
        // Sauvegarde de preset personnalisé
        this.elements.savePresetBtn.addEventListener('click', () => this.saveCustomPreset());
        
        // Boutons de test
        this.elements.refreshBtn.addEventListener('click', () => this.refreshSystemInfo());
        this.elements.resetBtn.addEventListener('click', () => this.resetEqualizer());
        this.elements.testEqBtn.addEventListener('click', () => this.testEqualizer());
        
        // Raccourcis clavier
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case ' ':
                    if (e.target.tagName !== 'INPUT') {
                        e.preventDefault();
                        this.toggleEqualizer();
                    }
                    break;
                case 'r':
                case 'R':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        this.refreshSystemInfo();
                    }
                    break;
                case 'Escape':
                    this.resetEqualizer();
                    break;
            }
        });
    }

    async connectToSystem() {
        try {
            this.setStatus('Connexion à l\'égaliseur système...', 'info');
            const response = await this.apiCall('get');
            
            if (response.success) {
                this.updateFromSettings(response.settings);
                this.isConnected = true;
                this.setStatus('Connecté à l\'égaliseur système', 'success');
                this.elements.apiStatus.textContent = 'Connecté';
                
            } else {
                throw new Error('Échec de connexion');
            }
        } catch (error) {
            console.error('Erreur connexion égaliseur:', error);
            this.setStatus('Erreur de connexion égaliseur', 'error');
            this.elements.apiStatus.textContent = 'Déconnecté';
            this.isConnected = false;
        }
    }

    async apiCall(action, params = {}) {
        const url = new URL(this.apiBaseUrl, window.location.origin);
        url.searchParams.append('action', action);
        
        Object.keys(params).forEach(key => {
            url.searchParams.append(key, params[key]);
        });

        const response = await fetch(url);
        return await response.json();
    }

    async setBand(band, gain) {
        gain = Math.max(-12, Math.min(12, gain));
        
        try {
            const response = await this.apiCall('set_band', { band, gain });
            
            if (response.success) {
                this.bands[band] = gain;
                this.updateBandDisplay(band, gain);
                this.setStatus(`${this.getBandName(band)}: ${gain > 0 ? '+' : ''}${gain} dB`, 'success');
                this.currentPreset = 'Personnalisé';
                this.updatePresetDisplay();
            } else {
                this.setStatus('Erreur réglage bande', 'error');
            }
        } catch (error) {
            console.error('Erreur API setBand:', error);
            this.setStatus('Erreur communication', 'error');
        }
    }

    async applyPreset(presetName) {
        try {
            const response = await this.apiCall('apply_preset', { preset: presetName });
            
            if (response.success) {
                this.updateFromSettings(response.settings);
                this.setStatus(`Preset "${presetName}" appliqué`, 'success');
            } else {
                this.setStatus('Erreur application preset', 'error');
            }
        } catch (error) {
            console.error('Erreur application preset:', error);
            this.setStatus('Erreur communication', 'error');
        }
    }

    async toggleEqualizer() {
        try {
            const response = await this.apiCall('toggle');
            
            if (response.success) {
                this.updateFromSettings(response.settings);
                this.setStatus(this.isEnabled ? 'Égaliseur activé' : 'Égaliseur désactivé', 'success');
            }
        } catch (error) {
            console.error('Erreur toggle égaliseur:', error);
            this.setStatus('Erreur activation/désactivation', 'error');
        }
    }

    async saveCustomPreset() {
        const name = this.elements.presetName.value.trim();
        
        if (!name) {
            this.setStatus('Veuillez entrer un nom de preset', 'warning');
            return;
        }
        
        try {
            const response = await this.apiCall('save_preset', { name });
            
            if (response.success) {
                this.customPresets[name] = { ...this.bands };
                this.addCustomPresetButton(name);
                this.elements.presetName.value = '';
                this.setStatus(`Preset "${name}" sauvegardé`, 'success');
            }
        } catch (error) {
            console.error('Erreur sauvegarde preset:', error);
            this.setStatus('Erreur sauvegarde', 'error');
        }
    }

    async resetEqualizer() {
        await this.applyPreset('Flat');
    }

    async testEqualizer() {
        this.setStatus('Test de l\'égaliseur...', 'info');
        
        const testSequence = [
            { preset: 'Bass_Boost', duration: 1000 },
            { preset: 'Treble_Boost', duration: 1000 },
            { preset: 'Vocal', duration: 1000 },
            { preset: 'Flat', duration: 500 }
        ];
        
        try {
            for (let i = 0; i < testSequence.length; i++) {
                const { preset, duration } = testSequence[i];
                await this.applyPreset(preset);
                this.setStatus(`Test ${i + 1}/${testSequence.length}: ${preset}`, 'info');
                await new Promise(resolve => setTimeout(resolve, duration));
            }
            
            this.setStatus('Test égaliseur terminé', 'success');
        } catch (error) {
            console.error('Erreur test égaliseur:', error);
            this.setStatus('Erreur pendant le test', 'error');
        }
    }

    async refreshSystemInfo() {
        this.setStatus('Actualisation...', 'info');
        await this.connectToSystem();
    }

    updateFromSettings(settings) {
        this.bands = { ...settings.bands };
        this.isEnabled = settings.enabled;
        this.currentPreset = settings.preset;
        this.platform = settings.platform;
        
        this.updateAllDisplays();
    }

    updateAllDisplays() {
        // Mettre à jour les sliders et valeurs
        Object.keys(this.bands).forEach(band => {
            this.updateBandDisplay(band, this.bands[band]);
        });
        
        // Mettre à jour l'état power
        this.updatePowerDisplay();
        
        // Mettre à jour les presets
        this.updatePresetDisplay();
        
        // Mettre à jour les informations
        this.updateInfoDisplay();
    }

    updateBandDisplay(band, gain) {
        const sliderMap = {
            bass: this.elements.bassSlider,
            low_mid: this.elements.lowMidSlider,
            mid: this.elements.midSlider,
            high_mid: this.elements.highMidSlider,
            treble: this.elements.trebleSlider
        };
        
        const valueMap = {
            bass: this.elements.bassValue,
            low_mid: this.elements.lowMidValue,
            mid: this.elements.midValue,
            high_mid: this.elements.highMidValue,
            treble: this.elements.trebleValue
        };
        
        if (sliderMap[band]) {
            sliderMap[band].value = gain;
        }
        
        if (valueMap[band]) {
            valueMap[band].textContent = `${gain > 0 ? '+' : ''}${gain} dB`;
        }
    }

    updatePowerDisplay() {
        if (this.isEnabled) {
            this.elements.powerBtn.classList.remove('off');
            this.elements.powerIcon.textContent = '⚡';
            this.elements.powerText.textContent = 'ON';
        } else {
            this.elements.powerBtn.classList.add('off');
            this.elements.powerIcon.textContent = '⏸️';
            this.elements.powerText.textContent = 'OFF';
        }
    }

    updatePresetDisplay() {
        this.elements.currentPreset.textContent = this.currentPreset;
        this.elements.currentPresetInfo.textContent = this.currentPreset;
        
        // Mettre à jour les boutons de preset
        this.elements.presetButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.preset === this.currentPreset) {
                btn.classList.add('active');
            }
        });
    }

    updateInfoDisplay() {
        this.elements.eqState.textContent = this.isEnabled ? 'Activé' : 'Désactivé';
        this.elements.platform.textContent = this.platform;
        this.elements.platformName.textContent = this.platform;
        
        if (this.platform === 'Simulation') {
            this.elements.equalizerType.textContent = 'Mode Simulation';
            this.elements.equalizerType.style.color = '#FF9800';
        } else {
            this.elements.equalizerType.textContent = 'Système Réel';
            this.elements.equalizerType.style.color = '#4CAF50';
        }
    }

    addCustomPresetButton(name) {
        const btn = document.createElement('button');
        btn.className = 'preset-btn';
        btn.textContent = name;
        btn.addEventListener('click', () => this.applyPreset(name));
        this.elements.savedPresets.appendChild(btn);
    }

    getBandName(band) {
        const names = {
            bass: 'Basses',
            low_mid: 'Médiums-Bas',
            mid: 'Médiums',
            high_mid: 'Médiums-Hauts',
            treble: 'Aigus'
        };
        return names[band] || band;
    }

    setStatus(message, type = 'info') {
        this.elements.statusText.textContent = message;
        
        const statusDot = this.elements.statusIndicator.querySelector('.status-dot');
        statusDot.className = 'status-dot';
        
        switch(type) {
            case 'success':
                statusDot.style.background = '#4CAF50';
                break;
            case 'warning':
                statusDot.style.background = '#FF9800';
                break;
            case 'error':
                statusDot.style.background = '#F44336';
                break;
            default:
                statusDot.style.background = '#2196F3';
        }
        
        // Effacer le message après 3 secondes (sauf pour les erreurs)
        if (type !== 'error') {
            setTimeout(() => {
                this.elements.statusText.textContent = 'Prêt';
                statusDot.style.background = '#4CAF50';
            }, 3000);
        }
    }

    startPeriodicUpdate() {
        // Actualiser les informations toutes les 10 secondes
        setInterval(async () => {
            if (this.isConnected) {
                try {
                    const response = await this.apiCall('get');
                    if (response.success) {
                        // Mettre à jour silencieusement si les réglages ont changé
                        const currentSettings = JSON.stringify(this.bands);
                        const newSettings = JSON.stringify(response.settings.bands);
                        
                        if (currentSettings !== newSettings || 
                            this.isEnabled !== response.settings.enabled) {
                            this.updateFromSettings(response.settings);
                        }
                    }
                } catch (error) {
                    // Ignorer les erreurs de mise à jour périodique
                }
            }
        }, 10000);
    }
}

// Initialiser l'égaliseur quand la page est chargée
document.addEventListener('DOMContentLoaded', () => {
    window.systemAudioEqualizer = new SystemAudioEqualizer();
});

// Gestion de la visibilité de la page
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.systemAudioEqualizer) {
        window.systemAudioEqualizer.refreshSystemInfo();
    }
});
