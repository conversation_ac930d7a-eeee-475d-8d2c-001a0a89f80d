// WaveCraft - Égaliseur Audio RÉEL JavaScript

class RealSystemAudioEqualizer {
    constructor() {
        this.bands = {
            bass: 0,
            low_mid: 0,
            mid: 0,
            high_mid: 0,
            treble: 0
        };
        this.isEnabled = true;
        this.currentPreset = 'Flat';
        this.platform = 'Windows';
        this.equalizerMethod = 'Détection...';
        this.apiBaseUrl = '/api/equalizer';
        this.isConnected = false;
        
        this.init();
    }

    async init() {
        this.setupElements();
        this.setupEventListeners();
        await this.connectToRealSystem();
        this.startPeriodicUpdate();
    }

    setupElements() {
        this.elements = {
            // Contrôles principaux
            powerBtn: document.getElementById('powerBtn'),
            powerIcon: document.getElementById('powerIcon'),
            powerText: document.getElementById('powerText'),
            currentPreset: document.getElementById('currentPreset'),
            methodBadge: document.getElementById('methodBadge'),
            
            // Sliders des bandes
            bassSlider: document.getElementById('bassSlider'),
            lowMidSlider: document.getElementById('lowMidSlider'),
            midSlider: document.getElementById('midSlider'),
            highMidSlider: document.getElementById('highMidSlider'),
            trebleSlider: document.getElementById('trebleSlider'),
            
            // Valeurs des bandes
            bassValue: document.getElementById('bassValue'),
            lowMidValue: document.getElementById('lowMidValue'),
            midValue: document.getElementById('midValue'),
            highMidValue: document.getElementById('highMidValue'),
            trebleValue: document.getElementById('trebleValue'),
            
            // Presets
            presetButtons: document.querySelectorAll('.preset-btn'),
            
            // Informations
            statusText: document.getElementById('statusText'),
            statusIndicator: document.getElementById('statusIndicator'),
            eqState: document.getElementById('eqState'),
            equalizerMethod: document.getElementById('equalizerMethod'),
            currentPresetInfo: document.getElementById('currentPresetInfo'),
            apiStatus: document.getElementById('apiStatus'),
            
            // Boutons de test
            refreshBtn: document.getElementById('refreshBtn'),
            resetBtn: document.getElementById('resetBtn'),
            testEqBtn: document.getElementById('testEqBtn')
        };
    }

    setupEventListeners() {
        // Sliders des bandes avec feedback immédiat
        this.elements.bassSlider.addEventListener('input', (e) => {
            this.setBandReal('bass', parseFloat(e.target.value));
        });
        
        this.elements.lowMidSlider.addEventListener('input', (e) => {
            this.setBandReal('low_mid', parseFloat(e.target.value));
        });
        
        this.elements.midSlider.addEventListener('input', (e) => {
            this.setBandReal('mid', parseFloat(e.target.value));
        });
        
        this.elements.highMidSlider.addEventListener('input', (e) => {
            this.setBandReal('high_mid', parseFloat(e.target.value));
        });
        
        this.elements.trebleSlider.addEventListener('input', (e) => {
            this.setBandReal('treble', parseFloat(e.target.value));
        });
        
        // Boutons de presets
        this.elements.presetButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const preset = e.target.dataset.preset;
                this.applyPresetReal(preset);
            });
        });
        
        // Boutons de test
        this.elements.refreshBtn.addEventListener('click', () => this.refreshSystemInfo());
        this.elements.resetBtn.addEventListener('click', () => this.resetEqualizer());
        this.elements.testEqBtn.addEventListener('click', () => this.testRealEqualizer());
        
        // Raccourcis clavier
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'r':
                case 'R':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        this.refreshSystemInfo();
                    }
                    break;
                case 'Escape':
                    this.resetEqualizer();
                    break;
                case 't':
                case 'T':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        this.testRealEqualizer();
                    }
                    break;
            }
        });
    }

    async connectToRealSystem() {
        try {
            this.setStatus('Connexion à l\'égaliseur système RÉEL...', 'info');
            const response = await this.apiCall('get');
            
            if (response.success) {
                this.updateFromSettings(response.settings);
                this.isConnected = true;
                this.setStatus('Connecté à l\'égaliseur système RÉEL', 'success');
                this.elements.apiStatus.textContent = 'Connecté RÉEL';
                
            } else {
                throw new Error('Échec de connexion égaliseur RÉEL');
            }
        } catch (error) {
            console.error('Erreur connexion égaliseur RÉEL:', error);
            this.setStatus('Erreur connexion égaliseur RÉEL', 'error');
            this.elements.apiStatus.textContent = 'Déconnecté';
            this.isConnected = false;
        }
    }

    async apiCall(action, params = {}) {
        const url = new URL(this.apiBaseUrl, window.location.origin);
        url.searchParams.append('action', action);
        
        Object.keys(params).forEach(key => {
            url.searchParams.append(key, params[key]);
        });

        const response = await fetch(url);
        return await response.json();
    }

    async setBandReal(band, gain) {
        gain = Math.max(-12, Math.min(12, gain));
        
        // Mise à jour immédiate de l'affichage
        this.updateBandDisplay(band, gain);
        
        try {
            const response = await this.apiCall('set_band', { band, gain });
            
            if (response.success) {
                this.bands[band] = gain;
                this.setStatus(`RÉEL: ${this.getBandName(band)} ${gain > 0 ? '+' : ''}${gain} dB`, 'success');
                this.currentPreset = 'Personnalisé';
                this.updatePresetDisplay();
            } else {
                this.setStatus('Erreur réglage bande RÉEL', 'error');
                // Restaurer la valeur précédente en cas d'erreur
                this.updateBandDisplay(band, this.bands[band]);
            }
        } catch (error) {
            console.error('Erreur API setBandReal:', error);
            this.setStatus('Erreur communication égaliseur RÉEL', 'error');
            // Restaurer la valeur précédente en cas d'erreur
            this.updateBandDisplay(band, this.bands[band]);
        }
    }

    async applyPresetReal(presetName) {
        try {
            this.setStatus(`Application preset RÉEL: ${presetName}...`, 'info');
            const response = await this.apiCall('apply_preset', { preset: presetName });
            
            if (response.success) {
                this.updateFromSettings(response.settings);
                this.setStatus(`✅ Preset RÉEL "${presetName}" appliqué au système`, 'success');
            } else {
                this.setStatus('Erreur application preset RÉEL', 'error');
            }
        } catch (error) {
            console.error('Erreur application preset RÉEL:', error);
            this.setStatus('Erreur communication preset RÉEL', 'error');
        }
    }

    async resetEqualizer() {
        this.setStatus('Reset égaliseur RÉEL...', 'info');
        await this.applyPresetReal('Flat');
    }

    async testRealEqualizer() {
        this.setStatus('🧪 Test RÉEL de l\'égaliseur système...', 'info');
        
        const testSequence = [
            { preset: 'Bass_Boost', duration: 2000, description: 'Test Basses' },
            { preset: 'Treble_Boost', duration: 2000, description: 'Test Aigus' },
            { preset: 'Vocal', duration: 2000, description: 'Test Voix' },
            { preset: 'Rock', duration: 2000, description: 'Test Rock' },
            { preset: 'Flat', duration: 1000, description: 'Retour Normal' }
        ];
        
        try {
            for (let i = 0; i < testSequence.length; i++) {
                const { preset, duration, description } = testSequence[i];
                
                this.setStatus(`🧪 Test RÉEL ${i + 1}/${testSequence.length}: ${description}`, 'info');
                await this.applyPresetReal(preset);
                
                // Attendre pour que l'utilisateur entende la différence
                await new Promise(resolve => setTimeout(resolve, duration));
            }
            
            this.setStatus('✅ Test RÉEL terminé - Égaliseur système testé', 'success');
        } catch (error) {
            console.error('Erreur test égaliseur RÉEL:', error);
            this.setStatus('❌ Erreur pendant le test RÉEL', 'error');
            // Restaurer le preset Flat en cas d'erreur
            await this.applyPresetReal('Flat');
        }
    }

    async refreshSystemInfo() {
        this.setStatus('Actualisation égaliseur RÉEL...', 'info');
        await this.connectToRealSystem();
    }

    updateFromSettings(settings) {
        this.bands = { ...settings.bands };
        this.isEnabled = settings.enabled;
        this.currentPreset = settings.preset;
        this.platform = settings.platform;
        this.equalizerMethod = settings.method || 'Inconnu';
        
        this.updateAllDisplays();
    }

    updateAllDisplays() {
        // Mettre à jour les sliders et valeurs
        Object.keys(this.bands).forEach(band => {
            this.updateBandDisplay(band, this.bands[band]);
        });
        
        // Mettre à jour les presets
        this.updatePresetDisplay();
        
        // Mettre à jour les informations
        this.updateInfoDisplay();
    }

    updateBandDisplay(band, gain) {
        const sliderMap = {
            bass: this.elements.bassSlider,
            low_mid: this.elements.lowMidSlider,
            mid: this.elements.midSlider,
            high_mid: this.elements.highMidSlider,
            treble: this.elements.trebleSlider
        };
        
        const valueMap = {
            bass: this.elements.bassValue,
            low_mid: this.elements.lowMidValue,
            mid: this.elements.midValue,
            high_mid: this.elements.highMidValue,
            treble: this.elements.trebleValue
        };
        
        if (sliderMap[band]) {
            sliderMap[band].value = gain;
        }
        
        if (valueMap[band]) {
            const displayValue = `${gain > 0 ? '+' : ''}${gain} dB`;
            valueMap[band].textContent = displayValue;
            
            // Colorer selon le gain
            if (gain > 0) {
                valueMap[band].style.color = '#4CAF50';
                valueMap[band].style.fontWeight = 'bold';
            } else if (gain < 0) {
                valueMap[band].style.color = '#FF5722';
                valueMap[band].style.fontWeight = 'bold';
            } else {
                valueMap[band].style.color = '#333';
                valueMap[band].style.fontWeight = 'normal';
            }
        }
    }

    updatePresetDisplay() {
        this.elements.currentPreset.textContent = this.currentPreset;
        this.elements.currentPresetInfo.textContent = this.currentPreset;
        
        // Mettre à jour les boutons de preset
        this.elements.presetButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.preset === this.currentPreset) {
                btn.classList.add('active');
            }
        });
    }

    updateInfoDisplay() {
        this.elements.eqState.textContent = this.isEnabled ? 'Activé RÉEL' : 'Désactivé';
        this.elements.equalizerMethod.textContent = this.equalizerMethod;
        this.elements.methodBadge.textContent = this.equalizerMethod;
        
        // Colorer selon la méthode
        if (this.equalizerMethod === 'EqualizerAPO') {
            this.elements.methodBadge.style.background = 'rgba(76, 175, 80, 0.8)';
        } else if (this.equalizerMethod === 'AudioDrivers') {
            this.elements.methodBadge.style.background = 'rgba(33, 150, 243, 0.8)';
        } else if (this.equalizerMethod === 'WindowsEffects') {
            this.elements.methodBadge.style.background = 'rgba(255, 152, 0, 0.8)';
        } else {
            this.elements.methodBadge.style.background = 'rgba(244, 67, 54, 0.8)';
        }
    }

    getBandName(band) {
        const names = {
            bass: 'Basses',
            low_mid: 'Médiums-Bas',
            mid: 'Médiums',
            high_mid: 'Médiums-Hauts',
            treble: 'Aigus'
        };
        return names[band] || band;
    }

    setStatus(message, type = 'info') {
        this.elements.statusText.textContent = message;
        
        const statusDot = this.elements.statusIndicator.querySelector('.status-dot');
        statusDot.className = 'status-dot';
        
        switch(type) {
            case 'success':
                statusDot.style.background = '#4CAF50';
                break;
            case 'warning':
                statusDot.style.background = '#FF9800';
                break;
            case 'error':
                statusDot.style.background = '#F44336';
                break;
            default:
                statusDot.style.background = '#2196F3';
        }
        
        // Effacer le message après 4 secondes (plus long pour les messages RÉEL)
        if (type !== 'error') {
            setTimeout(() => {
                this.elements.statusText.textContent = 'Égaliseur RÉEL prêt';
                statusDot.style.background = '#4CAF50';
            }, 4000);
        }
    }

    startPeriodicUpdate() {
        // Actualiser les informations toutes les 15 secondes (moins fréquent pour le RÉEL)
        setInterval(async () => {
            if (this.isConnected) {
                try {
                    const response = await this.apiCall('get');
                    if (response.success) {
                        // Mettre à jour silencieusement si les réglages ont changé
                        const currentSettings = JSON.stringify(this.bands);
                        const newSettings = JSON.stringify(response.settings.bands);
                        
                        if (currentSettings !== newSettings || 
                            this.isEnabled !== response.settings.enabled ||
                            this.equalizerMethod !== response.settings.method) {
                            this.updateFromSettings(response.settings);
                        }
                    }
                } catch (error) {
                    // Ignorer les erreurs de mise à jour périodique
                }
            }
        }, 15000);
    }
}

// Initialiser l'égaliseur RÉEL quand la page est chargée
document.addEventListener('DOMContentLoaded', () => {
    window.realSystemAudioEqualizer = new RealSystemAudioEqualizer();
    
    // Message d'accueil pour l'égaliseur RÉEL
    console.log('🎚️ WaveCraft - Égaliseur Audio Système RÉEL initialisé');
    console.log('✅ Cet égaliseur modifie VRAIMENT l\'audio de votre système');
    console.log('🎵 Écoutez de la musique pour entendre les changements');
});

// Gestion de la visibilité de la page
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.realSystemAudioEqualizer) {
        window.realSystemAudioEqualizer.refreshSystemInfo();
    }
});
