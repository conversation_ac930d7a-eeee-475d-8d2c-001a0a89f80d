// WaveCraft - Égaliseur Système Windows JavaScript

class WindowsSystemEqualizer {
    constructor() {
        this.bands = {
            bass: 0,
            low_mid: 0,
            mid: 0,
            high_mid: 0,
            treble: 0
        };
        this.isEnabled = true;
        this.currentPreset = 'Flat';
        this.platform = 'Windows';
        this.equalizerMethod = 'Détection...';
        this.deviceCount = 0;
        this.apiBaseUrl = '/api/equalizer';
        this.isConnected = false;
        
        this.init();
    }

    async init() {
        this.setupElements();
        this.setupEventListeners();
        await this.connectToSystemEqualizer();
        this.startPeriodicUpdate();
    }

    setupElements() {
        this.elements = {
            // Contrôles principaux
            powerBtn: document.getElementById('powerBtn'),
            powerIcon: document.getElementById('powerIcon'),
            powerText: document.getElementById('powerText'),
            currentPreset: document.getElementById('currentPreset'),
            
            // Informations système
            equalizerMethod: document.getElementById('equalizerMethod'),
            deviceCount: document.getElementById('deviceCount'),
            systemState: document.getElementById('systemState'),
            
            // Sliders des bandes
            bassSlider: document.getElementById('bassSlider'),
            lowMidSlider: document.getElementById('lowMidSlider'),
            midSlider: document.getElementById('midSlider'),
            highMidSlider: document.getElementById('highMidSlider'),
            trebleSlider: document.getElementById('trebleSlider'),
            
            // Valeurs des bandes
            bassValue: document.getElementById('bassValue'),
            lowMidValue: document.getElementById('lowMidValue'),
            midValue: document.getElementById('midValue'),
            highMidValue: document.getElementById('highMidValue'),
            trebleValue: document.getElementById('trebleValue'),
            
            // Presets
            presetButtons: document.querySelectorAll('.preset-btn'),
            
            // Informations
            statusText: document.getElementById('statusText'),
            statusIndicator: document.getElementById('statusIndicator'),
            eqState: document.getElementById('eqState'),
            equalizerMethodInfo: document.getElementById('equalizerMethodInfo'),
            currentPresetInfo: document.getElementById('currentPresetInfo'),
            apiStatus: document.getElementById('apiStatus'),
            
            // Boutons de test
            refreshBtn: document.getElementById('refreshBtn'),
            resetBtn: document.getElementById('resetBtn'),
            testEqBtn: document.getElementById('testEqBtn')
        };
    }

    setupEventListeners() {
        // Bouton power
        this.elements.powerBtn.addEventListener('click', () => this.toggleEqualizer());
        
        // Sliders des bandes avec feedback immédiat
        this.elements.bassSlider.addEventListener('input', (e) => {
            this.setBandSystem('bass', parseFloat(e.target.value));
        });
        
        this.elements.lowMidSlider.addEventListener('input', (e) => {
            this.setBandSystem('low_mid', parseFloat(e.target.value));
        });
        
        this.elements.midSlider.addEventListener('input', (e) => {
            this.setBandSystem('mid', parseFloat(e.target.value));
        });
        
        this.elements.highMidSlider.addEventListener('input', (e) => {
            this.setBandSystem('high_mid', parseFloat(e.target.value));
        });
        
        this.elements.trebleSlider.addEventListener('input', (e) => {
            this.setBandSystem('treble', parseFloat(e.target.value));
        });
        
        // Boutons de presets
        this.elements.presetButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const preset = e.target.dataset.preset;
                this.applyPresetSystem(preset);
            });
        });
        
        // Boutons de test
        this.elements.refreshBtn.addEventListener('click', () => this.refreshSystemInfo());
        this.elements.resetBtn.addEventListener('click', () => this.resetEqualizer());
        this.elements.testEqBtn.addEventListener('click', () => this.testSystemEqualizer());
        
        // Raccourcis clavier
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case ' ':
                    if (e.target.tagName !== 'INPUT') {
                        e.preventDefault();
                        this.toggleEqualizer();
                    }
                    break;
                case 'r':
                case 'R':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        this.refreshSystemInfo();
                    }
                    break;
                case 'Escape':
                    this.resetEqualizer();
                    break;
                case 't':
                case 'T':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        this.testSystemEqualizer();
                    }
                    break;
            }
        });
    }

    async connectToSystemEqualizer() {
        try {
            this.setStatus('Connexion à l\'égaliseur système Windows...', 'info');
            const response = await this.apiCall('get');
            
            if (response.success) {
                this.updateFromSettings(response.settings);
                this.isConnected = true;
                this.setStatus('Connecté à l\'égaliseur système Windows', 'success');
                this.elements.apiStatus.textContent = 'Connecté SYSTÈME';
                
            } else {
                throw new Error('Échec de connexion égaliseur système');
            }
        } catch (error) {
            console.error('Erreur connexion égaliseur système:', error);
            this.setStatus('Erreur connexion égaliseur système', 'error');
            this.elements.apiStatus.textContent = 'Déconnecté';
            this.isConnected = false;
        }
    }

    async apiCall(action, params = {}) {
        const url = new URL(this.apiBaseUrl, window.location.origin);
        url.searchParams.append('action', action);
        
        Object.keys(params).forEach(key => {
            url.searchParams.append(key, params[key]);
        });

        const response = await fetch(url);
        return await response.json();
    }

    async setBandSystem(band, gain) {
        gain = Math.max(-12, Math.min(12, gain));
        
        // Mise à jour immédiate de l'affichage
        this.updateBandDisplay(band, gain);
        
        try {
            const response = await this.apiCall('set_band', { band, gain });
            
            if (response.success) {
                this.bands[band] = gain;
                this.setStatus(`SYSTÈME: ${this.getBandName(band)} ${gain > 0 ? '+' : ''}${gain} dB`, 'success');
                this.currentPreset = 'Personnalisé';
                this.updatePresetDisplay();
            } else {
                this.setStatus('Erreur réglage bande système', 'error');
                // Restaurer la valeur précédente en cas d'erreur
                this.updateBandDisplay(band, this.bands[band]);
            }
        } catch (error) {
            console.error('Erreur API setBandSystem:', error);
            this.setStatus('Erreur communication égaliseur système', 'error');
            // Restaurer la valeur précédente en cas d'erreur
            this.updateBandDisplay(band, this.bands[band]);
        }
    }

    async applyPresetSystem(presetName) {
        try {
            this.setStatus(`Application preset système: ${presetName}...`, 'info');
            const response = await this.apiCall('apply_preset', { preset: presetName });
            
            if (response.success) {
                this.updateFromSettings(response.settings);
                this.setStatus(`✅ Preset SYSTÈME "${presetName}" appliqué`, 'success');
            } else {
                this.setStatus('Erreur application preset système', 'error');
            }
        } catch (error) {
            console.error('Erreur application preset système:', error);
            this.setStatus('Erreur communication preset système', 'error');
        }
    }

    async toggleEqualizer() {
        try {
            const response = await this.apiCall('toggle');
            
            if (response.success) {
                this.updateFromSettings(response.settings);
                this.setStatus(this.isEnabled ? 'Égaliseur système activé' : 'Égaliseur système désactivé', 'success');
            }
        } catch (error) {
            console.error('Erreur toggle égaliseur système:', error);
            this.setStatus('Erreur activation/désactivation système', 'error');
        }
    }

    async resetEqualizer() {
        this.setStatus('Reset égaliseur système...', 'info');
        await this.applyPresetSystem('Flat');
    }

    async testSystemEqualizer() {
        this.setStatus('🧪 Test SYSTÈME de l\'égaliseur Windows...', 'info');
        
        const testSequence = [
            { preset: 'Bass_Boost', duration: 3000, description: 'Test Basses Système' },
            { preset: 'Treble_Boost', duration: 3000, description: 'Test Aigus Système' },
            { preset: 'Vocal', duration: 3000, description: 'Test Voix Système' },
            { preset: 'Rock', duration: 3000, description: 'Test Rock Système' },
            { preset: 'Flat', duration: 1000, description: 'Retour Normal' }
        ];
        
        try {
            for (let i = 0; i < testSequence.length; i++) {
                const { preset, duration, description } = testSequence[i];
                
                this.setStatus(`🧪 Test SYSTÈME ${i + 1}/${testSequence.length}: ${description}`, 'info');
                await this.applyPresetSystem(preset);
                
                // Attendre pour que l'utilisateur entende la différence
                await new Promise(resolve => setTimeout(resolve, duration));
            }
            
            this.setStatus('✅ Test SYSTÈME terminé - Égaliseur Windows testé', 'success');
        } catch (error) {
            console.error('Erreur test égaliseur système:', error);
            this.setStatus('❌ Erreur pendant le test SYSTÈME', 'error');
            // Restaurer le preset Flat en cas d'erreur
            await this.applyPresetSystem('Flat');
        }
    }

    async refreshSystemInfo() {
        this.setStatus('Actualisation égaliseur système...', 'info');
        await this.connectToSystemEqualizer();
    }

    updateFromSettings(settings) {
        this.bands = { ...settings.bands };
        this.isEnabled = settings.enabled;
        this.currentPreset = settings.preset;
        this.platform = settings.platform;
        this.equalizerMethod = settings.method || 'Registry Control';
        this.deviceCount = settings.devices || 0;
        
        this.updateAllDisplays();
    }

    updateAllDisplays() {
        // Mettre à jour les sliders et valeurs
        Object.keys(this.bands).forEach(band => {
            this.updateBandDisplay(band, this.bands[band]);
        });
        
        // Mettre à jour l'état power
        this.updatePowerDisplay();
        
        // Mettre à jour les presets
        this.updatePresetDisplay();
        
        // Mettre à jour les informations système
        this.updateSystemInfo();
    }

    updateBandDisplay(band, gain) {
        const sliderMap = {
            bass: this.elements.bassSlider,
            low_mid: this.elements.lowMidSlider,
            mid: this.elements.midSlider,
            high_mid: this.elements.highMidSlider,
            treble: this.elements.trebleSlider
        };
        
        const valueMap = {
            bass: this.elements.bassValue,
            low_mid: this.elements.lowMidValue,
            mid: this.elements.midValue,
            high_mid: this.elements.highMidValue,
            treble: this.elements.trebleValue
        };
        
        if (sliderMap[band]) {
            sliderMap[band].value = gain;
        }
        
        if (valueMap[band]) {
            const displayValue = `${gain > 0 ? '+' : ''}${gain} dB`;
            valueMap[band].textContent = displayValue;
            
            // Colorer selon le gain (style système)
            if (gain > 0) {
                valueMap[band].style.color = '#FF9800';
                valueMap[band].style.fontWeight = 'bold';
            } else if (gain < 0) {
                valueMap[band].style.color = '#FF5722';
                valueMap[band].style.fontWeight = 'bold';
            } else {
                valueMap[band].style.color = '#333';
                valueMap[band].style.fontWeight = 'normal';
            }
        }
    }

    updatePowerDisplay() {
        if (this.isEnabled) {
            this.elements.powerBtn.classList.remove('off');
            this.elements.powerIcon.textContent = '⚡';
            this.elements.powerText.textContent = 'SYSTÈME';
        } else {
            this.elements.powerBtn.classList.add('off');
            this.elements.powerIcon.textContent = '⏸️';
            this.elements.powerText.textContent = 'OFF';
        }
    }

    updatePresetDisplay() {
        this.elements.currentPreset.textContent = this.currentPreset;
        this.elements.currentPresetInfo.textContent = this.currentPreset;
        
        // Mettre à jour les boutons de preset
        this.elements.presetButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.preset === this.currentPreset) {
                btn.classList.add('active');
            }
        });
    }

    updateSystemInfo() {
        this.elements.eqState.textContent = this.isEnabled ? 'Activé SYSTÈME' : 'Désactivé';
        this.elements.equalizerMethod.textContent = this.equalizerMethod;
        this.elements.equalizerMethodInfo.textContent = this.equalizerMethod;
        this.elements.deviceCount.textContent = `${this.deviceCount} périphériques`;
        this.elements.systemState.textContent = this.isEnabled ? 'Opérationnel' : 'Arrêté';
    }

    getBandName(band) {
        const names = {
            bass: 'Basses',
            low_mid: 'Médiums-Bas',
            mid: 'Médiums',
            high_mid: 'Médiums-Hauts',
            treble: 'Aigus'
        };
        return names[band] || band;
    }

    setStatus(message, type = 'info') {
        this.elements.statusText.textContent = message;
        
        const statusDot = this.elements.statusIndicator.querySelector('.status-dot');
        statusDot.className = 'status-dot';
        
        switch(type) {
            case 'success':
                statusDot.style.background = '#FF9800';
                break;
            case 'warning':
                statusDot.style.background = '#FF9800';
                break;
            case 'error':
                statusDot.style.background = '#F44336';
                break;
            default:
                statusDot.style.background = '#FF9800';
        }
        
        // Effacer le message après 4 secondes
        if (type !== 'error') {
            setTimeout(() => {
                this.elements.statusText.textContent = 'Égaliseur SYSTÈME prêt';
                statusDot.style.background = '#FF9800';
            }, 4000);
        }
    }

    startPeriodicUpdate() {
        // Actualiser les informations toutes les 15 secondes
        setInterval(async () => {
            if (this.isConnected) {
                try {
                    const response = await this.apiCall('get');
                    if (response.success) {
                        // Mettre à jour silencieusement si les réglages ont changé
                        const currentSettings = JSON.stringify(this.bands);
                        const newSettings = JSON.stringify(response.settings.bands);
                        
                        if (currentSettings !== newSettings || 
                            this.isEnabled !== response.settings.enabled ||
                            this.equalizerMethod !== response.settings.method) {
                            this.updateFromSettings(response.settings);
                        }
                    }
                } catch (error) {
                    // Ignorer les erreurs de mise à jour périodique
                }
            }
        }, 15000);
    }
}

// Initialiser l'égaliseur SYSTÈME quand la page est chargée
document.addEventListener('DOMContentLoaded', () => {
    window.windowsSystemEqualizer = new WindowsSystemEqualizer();
    
    // Message d'accueil pour l'égaliseur SYSTÈME
    console.log('🎚️ WaveCraft - Égaliseur Système Windows initialisé');
    console.log('🖥️ Utilise les APIs Windows natives pour contrôler l\'audio système');
    console.log('🎵 Modifie directement les paramètres audio de Windows');
});

// Gestion de la visibilité de la page
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.windowsSystemEqualizer) {
        window.windowsSystemEqualizer.refreshSystemInfo();
    }
});
