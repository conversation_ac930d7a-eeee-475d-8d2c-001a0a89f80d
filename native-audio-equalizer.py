#!/usr/bin/env python3
"""
WaveCraft - Égaliseur Audio Natif
Égaliseur audio complet codé depuis zéro sans dépendances externes
Utilise les APIs Windows natives pour traiter l'audio système
"""

import sys
import json
import threading
import time
import math
import numpy as np
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import webbrowser

# Vérification plateforme Windows
if sys.platform != "win32":
    print("❌ ERREUR: Cette application ne fonctionne que sur Windows")
    sys.exit(1)

try:
    import ctypes
    from ctypes import wintypes, windll, POINTER, Structure, c_float, c_int, c_void_p, byref
    import wave
    import pyaudio
    print("✅ Modules audio natifs chargés avec succès")
except ImportError as e:
    print(f"❌ ERREUR: Modules manquants: {e}")
    print("📦 Installation requise: pip install pyaudio numpy")
    sys.exit(1)

class AudioFilter:
    """Filtre audio pour égalisation"""
    
    def __init__(self, sample_rate=44100):
        self.sample_rate = sample_rate
        self.filters = {}
        self.setup_filters()
    
    def setup_filters(self):
        """Configure les filtres pour chaque bande"""
        # Définition des bandes de fréquences
        self.bands = {
            'bass': {'freq': 125, 'q': 1.0, 'gain': 0.0},      # 60-250 Hz
            'low_mid': {'freq': 375, 'q': 1.0, 'gain': 0.0},   # 250-500 Hz
            'mid': {'freq': 1250, 'q': 1.0, 'gain': 0.0},      # 500-2000 Hz
            'high_mid': {'freq': 3000, 'q': 1.0, 'gain': 0.0}, # 2000-4000 Hz
            'treble': {'freq': 8000, 'q': 1.0, 'gain': 0.0}    # 4000-16000 Hz
        }
        
        # Initialiser les filtres biquad pour chaque bande
        for band, params in self.bands.items():
            self.filters[band] = self.create_biquad_filter(
                params['freq'], params['q'], params['gain']
            )
    
    def create_biquad_filter(self, freq, q, gain_db):
        """Crée un filtre biquad pour une bande de fréquence"""
        # Conversion du gain en linéaire
        gain = 10 ** (gain_db / 20.0)
        
        # Calcul des coefficients du filtre biquad
        omega = 2 * math.pi * freq / self.sample_rate
        sin_omega = math.sin(omega)
        cos_omega = math.cos(omega)
        alpha = sin_omega / (2 * q)
        
        # Coefficients pour un filtre peaking EQ
        A = gain
        
        b0 = 1 + alpha * A
        b1 = -2 * cos_omega
        b2 = 1 - alpha * A
        a0 = 1 + alpha / A
        a1 = -2 * cos_omega
        a2 = 1 - alpha / A
        
        # Normalisation
        b0 /= a0
        b1 /= a0
        b2 /= a0
        a1 /= a0
        a2 /= a0
        
        return {
            'b0': b0, 'b1': b1, 'b2': b2,
            'a1': a1, 'a2': a2,
            'x1': 0.0, 'x2': 0.0,  # États précédents d'entrée
            'y1': 0.0, 'y2': 0.0   # États précédents de sortie
        }
    
    def update_band_gain(self, band, gain_db):
        """Met à jour le gain d'une bande"""
        if band in self.bands:
            self.bands[band]['gain'] = gain_db
            self.filters[band] = self.create_biquad_filter(
                self.bands[band]['freq'],
                self.bands[band]['q'],
                gain_db
            )
            return True
        return False
    
    def process_sample(self, sample):
        """Traite un échantillon audio à travers tous les filtres"""
        output = sample
        
        # Appliquer chaque filtre en série
        for band, filt in self.filters.items():
            # Équation du filtre biquad
            y = (filt['b0'] * output + 
                 filt['b1'] * filt['x1'] + 
                 filt['b2'] * filt['x2'] - 
                 filt['a1'] * filt['y1'] - 
                 filt['a2'] * filt['y2'])
            
            # Mise à jour des états
            filt['x2'] = filt['x1']
            filt['x1'] = output
            filt['y2'] = filt['y1']
            filt['y1'] = y
            
            output = y
        
        return output
    
    def process_buffer(self, audio_data):
        """Traite un buffer audio complet"""
        # Convertir en numpy array pour traitement efficace
        samples = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32)
        
        # Normaliser (-1.0 à 1.0)
        samples = samples / 32768.0
        
        # Traiter chaque échantillon
        processed = np.zeros_like(samples)
        for i, sample in enumerate(samples):
            processed[i] = self.process_sample(sample)
        
        # Reconvertir en int16
        processed = np.clip(processed * 32768.0, -32768, 32767).astype(np.int16)
        
        return processed.tobytes()

class NativeAudioEqualizer:
    """Égaliseur audio natif utilisant PyAudio pour l'interception système"""
    
    def __init__(self):
        self.is_running = False
        self.audio_filter = AudioFilter()
        self.input_stream = None
        self.output_stream = None
        self.audio = None
        self.current_preset = "Flat"
        self.is_enabled = True
        
        self.initialize_audio()
    
    def initialize_audio(self):
        """Initialise le système audio"""
        try:
            self.audio = pyaudio.PyAudio()
            
            # Configuration audio
            self.format = pyaudio.paInt16
            self.channels = 2  # Stéréo
            self.rate = 44100  # 44.1 kHz
            self.chunk = 1024  # Taille du buffer
            
            print("✅ Système audio natif initialisé")
            print(f"📊 Format: {self.channels} canaux, {self.rate} Hz")
            
            # Lister les périphériques audio disponibles
            self.list_audio_devices()
            
        except Exception as e:
            print(f"❌ Erreur initialisation audio: {e}")
            raise
    
    def list_audio_devices(self):
        """Liste les périphériques audio disponibles"""
        print("\n🎵 Périphériques audio détectés:")
        
        device_count = self.audio.get_device_count()
        for i in range(device_count):
            device_info = self.audio.get_device_info_by_index(i)
            print(f"  {i}: {device_info['name']} "
                  f"(In: {device_info['maxInputChannels']}, "
                  f"Out: {device_info['maxOutputChannels']})")
    
    def start_audio_processing(self):
        """Démarre le traitement audio en temps réel"""
        if self.is_running:
            return True
        
        try:
            # Callback pour traitement audio
            def audio_callback(in_data, frame_count, time_info, status):
                if self.is_enabled:
                    # Traiter l'audio avec l'égaliseur
                    processed_data = self.audio_filter.process_buffer(in_data)
                    return (processed_data, pyaudio.paContinue)
                else:
                    # Passer l'audio sans traitement
                    return (in_data, pyaudio.paContinue)
            
            # Ouvrir les flux d'entrée et de sortie
            self.input_stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.rate,
                input=True,
                frames_per_buffer=self.chunk,
                stream_callback=audio_callback
            )
            
            self.output_stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.rate,
                output=True,
                frames_per_buffer=self.chunk
            )
            
            # Démarrer les flux
            self.input_stream.start_stream()
            self.output_stream.start_stream()
            
            self.is_running = True
            print("✅ Traitement audio en temps réel démarré")
            return True
            
        except Exception as e:
            print(f"❌ Erreur démarrage audio: {e}")
            return False
    
    def stop_audio_processing(self):
        """Arrête le traitement audio"""
        if not self.is_running:
            return
        
        try:
            if self.input_stream:
                self.input_stream.stop_stream()
                self.input_stream.close()
            
            if self.output_stream:
                self.output_stream.stop_stream()
                self.output_stream.close()
            
            self.is_running = False
            print("🛑 Traitement audio arrêté")
            
        except Exception as e:
            print(f"❌ Erreur arrêt audio: {e}")
    
    def set_band_gain(self, band, gain_db):
        """Définit le gain d'une bande de fréquence"""
        gain_db = max(-12, min(12, gain_db))
        
        if self.audio_filter.update_band_gain(band, gain_db):
            print(f"✅ Bande {band}: {gain_db:+.1f} dB")
            return True
        else:
            print(f"❌ Erreur bande {band}")
            return False
    
    def apply_preset(self, preset_name):
        """Applique un preset d'égalisation"""
        presets = {
            "Flat": {"bass": 0, "low_mid": 0, "mid": 0, "high_mid": 0, "treble": 0},
            "Rock": {"bass": 3, "low_mid": 1, "mid": -1, "high_mid": 2, "treble": 4},
            "Pop": {"bass": 2, "low_mid": 0, "mid": 1, "high_mid": 2, "treble": 3},
            "Jazz": {"bass": 1, "low_mid": 2, "mid": 1, "high_mid": 0, "treble": 2},
            "Classical": {"bass": 0, "low_mid": 1, "mid": 2, "high_mid": 1, "treble": 0},
            "Electronic": {"bass": 4, "low_mid": 2, "mid": 0, "high_mid": 3, "treble": 5},
            "Vocal": {"bass": -1, "low_mid": 1, "mid": 3, "high_mid": 2, "treble": 0},
            "Bass_Boost": {"bass": 6, "low_mid": 3, "mid": 0, "high_mid": 0, "treble": 0},
            "Treble_Boost": {"bass": 0, "low_mid": 0, "mid": 0, "high_mid": 3, "treble": 6}
        }
        
        if preset_name not in presets:
            return False
        
        preset = presets[preset_name]
        success = True
        
        print(f"🎼 Application preset: {preset_name}")
        for band, gain in preset.items():
            if not self.set_band_gain(band, gain):
                success = False
        
        if success:
            self.current_preset = preset_name
            print(f"✅ Preset '{preset_name}' appliqué")
        
        return success
    
    def toggle_equalizer(self):
        """Active/désactive l'égaliseur"""
        self.is_enabled = not self.is_enabled
        status = "activé" if self.is_enabled else "désactivé"
        print(f"🎚️ Égaliseur {status}")
        return True
    
    def get_current_settings(self):
        """Récupère les réglages actuels"""
        bands = {}
        for band, params in self.audio_filter.bands.items():
            bands[band] = params['gain']
        
        return {
            'bands': bands,
            'enabled': self.is_enabled,
            'preset': self.current_preset,
            'platform': 'Windows',
            'method': 'Native Audio Processing'
        }
    
    def cleanup(self):
        """Nettoie les ressources"""
        self.stop_audio_processing()
        if self.audio:
            self.audio.terminate()

# Instance globale de l'égaliseur natif
try:
    native_equalizer = NativeAudioEqualizer()
    print("🎚️ Égaliseur audio natif initialisé avec succès")
except Exception as e:
    print(f"❌ ERREUR CRITIQUE: {e}")
    sys.exit(1)

class NativeEqualizerHTTPHandler(BaseHTTPRequestHandler):
    """Serveur HTTP pour l'égaliseur natif"""
    
    def do_GET(self):
        """Gère les requêtes GET"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.serve_main_page()
        elif parsed_path.path == '/api/equalizer':
            self.handle_equalizer_api(parsed_path.query)
        elif parsed_path.path.endswith('.css'):
            self.serve_static_file('equalizer-styles.css', 'text/css')
        elif parsed_path.path.endswith('.js'):
            self.serve_static_file('native-equalizer.js', 'application/javascript')
        else:
            self.send_error(404)
    
    def serve_main_page(self):
        """Sert la page principale"""
        try:
            with open('native-equalizer.html', 'r', encoding='utf-8') as f:
                content = f.read()
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, "Page non trouvée")
    
    def serve_static_file(self, filename, content_type):
        """Sert les fichiers statiques"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            self.send_response(200)
            self.send_header('Content-type', content_type)
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404)
    
    def handle_equalizer_api(self, query):
        """Gère l'API de l'égaliseur natif"""
        params = parse_qs(query)
        action = params.get('action', [''])[0]
        
        response_data = {
            'success': False,
            'settings': native_equalizer.get_current_settings()
        }
        
        if action == 'get':
            response_data['success'] = True
            
        elif action == 'set_band':
            band = params.get('band', [''])[0]
            gain = float(params.get('gain', [0])[0])
            response_data['success'] = native_equalizer.set_band_gain(band, gain)
            response_data['settings'] = native_equalizer.get_current_settings()
            
        elif action == 'apply_preset':
            preset = params.get('preset', [''])[0]
            response_data['success'] = native_equalizer.apply_preset(preset)
            response_data['settings'] = native_equalizer.get_current_settings()
            
        elif action == 'toggle':
            response_data['success'] = native_equalizer.toggle_equalizer()
            response_data['settings'] = native_equalizer.get_current_settings()
            
        elif action == 'start_processing':
            response_data['success'] = native_equalizer.start_audio_processing()
            response_data['settings'] = native_equalizer.get_current_settings()
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response_data).encode('utf-8'))
    
    def log_message(self, format, *args):
        """Supprime les logs HTTP"""
        pass

def start_native_equalizer_server():
    """Démarre le serveur égaliseur natif"""
    server_address = ('localhost', 8083)
    httpd = HTTPServer(server_address, NativeEqualizerHTTPHandler)
    
    print(f"\n🎚️ WaveCraft - Égaliseur Audio Natif")
    print(f"📱 Plateforme: Windows")
    print(f"🔧 Méthode: Traitement Audio Natif")
    print(f"🌐 Serveur démarré sur http://localhost:8083")
    print(f"🎵 État: {'Activé' if native_equalizer.is_enabled else 'Désactivé'}")
    print(f"🎼 Preset: {native_equalizer.current_preset}")
    print("\n💡 Ouvrez votre navigateur sur http://localhost:8083")
    print("⏹️  Appuyez sur Ctrl+C pour arrêter\n")
    
    # Ouvrir automatiquement le navigateur
    threading.Timer(1.0, lambda: webbrowser.open('http://localhost:8083')).start()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur égaliseur natif...")
        native_equalizer.cleanup()
        httpd.shutdown()

if __name__ == "__main__":
    start_native_equalizer_server()
