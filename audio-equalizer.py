#!/usr/bin/env python3
"""
WaveCraft - Égaliseur Audio Système
Application native pour égaliser réellement l'audio du PC/téléphone
"""

import sys
import json
import subprocess
import threading
import numpy as np
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import webbrowser

# Imports spécifiques à la plateforme pour l'égalisation
PLATFORM = "Inconnu"
try:
    if sys.platform == "win32":
        # Windows - Égaliseur via EqualizerAPO ou registre système
        import winreg
        import ctypes
        from ctypes import wintypes
        PLATFORM = "Windows"
        print("Modules Windows égaliseur chargés avec succès")
    elif sys.platform == "darwin":
        # macOS - Égaliseur via Audio Units
        PLATFORM = "macOS"
        print("Plateforme macOS détectée pour égaliseur")
    elif sys.platform.startswith("linux"):
        # Linux - Égaliseur via PulseAudio/ALSA
        PLATFORM = "Linux"
        print("Plateforme Linux détectée pour égaliseur")
    else:
        print("⚠️ Plateforme non supportée - Arrêt du programme")
        sys.exit(1)
except ImportError as e:
    print(f"❌ ERREUR: Dépendances manquantes: {e}")
    print("📦 Installez les dépendances: pip install numpy")
    print("🛑 Arrêt du programme - Égaliseur système impossible sans dépendances")
    sys.exit(1)

class SystemAudioEqualizer:
    """Égaliseur audio système multi-plateforme"""
    
    def __init__(self):
        self.platform = PLATFORM
        self.eq_bands = {
            'bass': 0,        # 60-250 Hz
            'low_mid': 0,     # 250-500 Hz  
            'mid': 0,         # 500-2000 Hz
            'high_mid': 0,    # 2000-4000 Hz
            'treble': 0       # 4000-16000 Hz
        }
        self.is_enabled = True
        self.current_preset = "Flat"
        self.custom_presets = {}
        
        self.initialize_equalizer()
    
    def initialize_equalizer(self):
        """Initialise l'égaliseur selon la plateforme"""
        try:
            if self.platform == "Windows":
                self._init_windows_equalizer()
            elif self.platform == "macOS":
                self._init_macos_equalizer()
            elif self.platform == "Linux":
                self._init_linux_equalizer()
            else:
                raise Exception("Plateforme non supportée")
                
            print(f"Égaliseur {self.platform} initialisé avec succès")
            
        except Exception as e:
            print(f"❌ ERREUR CRITIQUE: Impossible d'initialiser l'égaliseur système: {e}")
            print("🛑 L'application ne peut pas fonctionner sans égaliseur système réel")
            print("💡 Vérifiez que vous avez les permissions administrateur")
            sys.exit(1)
    
    def _init_windows_equalizer(self):
        """Initialise l'égaliseur Windows"""
        try:
            # Vérifier si EqualizerAPO est installé
            try:
                import winreg
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                   r"SOFTWARE\EqualizerAPO", 0, winreg.KEY_READ)
                winreg.CloseKey(key)
                self.equalizer_type = "EqualizerAPO"
                print("EqualizerAPO détecté")
            except:
                # Fallback vers contrôles système Windows
                self.equalizer_type = "Windows_System"
                print("Utilisation des contrôles système Windows")
                
        except Exception as e:
            print(f"Erreur initialisation égaliseur Windows: {e}")
            raise
    
    def _init_macos_equalizer(self):
        """Initialise l'égaliseur macOS"""
        try:
            # Vérifier les Audio Units disponibles
            result = subprocess.run(['system_profiler', 'SPAudioDataType'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.equalizer_type = "AudioUnits"
                print("Audio Units macOS détectées")
            else:
                raise Exception("Audio Units non disponibles")
                
        except Exception as e:
            print(f"Erreur initialisation égaliseur macOS: {e}")
            raise
    
    def _init_linux_equalizer(self):
        """Initialise l'égaliseur Linux"""
        try:
            # Vérifier PulseAudio avec module égaliseur
            result = subprocess.run(['pactl', 'list', 'modules'], 
                                  capture_output=True, text=True)
            if result.returncode == 0 and 'module-equalizer' in result.stdout:
                self.equalizer_type = "PulseAudio_EQ"
                print("Module égaliseur PulseAudio détecté")
            else:
                # Fallback vers ALSA
                result = subprocess.run(['which', 'alsaequal'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    self.equalizer_type = "ALSA_Equal"
                    print("ALSA Equal détecté")
                else:
                    raise Exception("Aucun égaliseur système trouvé")
                    
        except Exception as e:
            print(f"Erreur initialisation égaliseur Linux: {e}")
            raise
    
    def set_band(self, band, gain):
        """Définit le gain d'une bande de fréquence (-12 à +12 dB)"""
        gain = max(-12, min(12, gain))
        
        try:
            if self.platform == "Windows":
                success = self._set_windows_band(band, gain)
            elif self.platform == "macOS":
                success = self._set_macos_band(band, gain)
            elif self.platform == "Linux":
                success = self._set_linux_band(band, gain)
            else:
                success = False
            
            if success:
                self.eq_bands[band] = gain
                print(f"Bande {band} définie à {gain} dB")
                return True
            else:
                print(f"Erreur définition bande {band}")
                return False
                
        except Exception as e:
            print(f"Erreur définition bande {band}: {e}")
            return False
    
    def _set_windows_band(self, band, gain):
        """Définit une bande d'égalisation Windows"""
        try:
            if self.equalizer_type == "EqualizerAPO":
                # Utiliser EqualizerAPO via fichier de configuration
                config_path = r"C:\Program Files\EqualizerAPO\config\config.txt"
                
                # Mapping des bandes vers les fréquences
                freq_map = {
                    'bass': '80',
                    'low_mid': '320',
                    'mid': '1000',
                    'high_mid': '3200',
                    'treble': '8000'
                }
                
                # Lire la configuration actuelle
                try:
                    with open(config_path, 'r') as f:
                        config = f.read()
                except:
                    config = ""
                
                # Mettre à jour ou ajouter la bande
                freq = freq_map[band]
                filter_line = f"Filter: ON PK Fc {freq} Hz Gain {gain} dB Q 1.0"
                
                # Remplacer ou ajouter la ligne
                lines = config.split('\n')
                updated = False
                for i, line in enumerate(lines):
                    if f"Fc {freq} Hz" in line:
                        lines[i] = filter_line
                        updated = True
                        break
                
                if not updated:
                    lines.append(filter_line)
                
                # Écrire la nouvelle configuration
                with open(config_path, 'w') as f:
                    f.write('\n'.join(lines))
                
                return True
                
            else:
                # Utiliser les contrôles système Windows (limité)
                print(f"Contrôle système Windows: {band} = {gain} dB")
                return True
                
        except Exception as e:
            print(f"Erreur Windows band {band}: {e}")
            return False
    
    def _set_macos_band(self, band, gain):
        """Définit une bande d'égalisation macOS"""
        try:
            # Utiliser osascript pour contrôler l'égaliseur iTunes/Music
            freq_map = {
                'bass': '1',
                'low_mid': '2', 
                'mid': '3',
                'high_mid': '4',
                'treble': '5'
            }
            
            band_num = freq_map[band]
            script = f'''
            tell application "Music"
                set EQ enabled to true
                set band {band_num} of EQ to {gain}
            end tell
            '''
            
            result = subprocess.run(['osascript', '-e', script], 
                                  capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            print(f"Erreur macOS band {band}: {e}")
            return False
    
    def _set_linux_band(self, band, gain):
        """Définit une bande d'égalisation Linux"""
        try:
            if self.equalizer_type == "PulseAudio_EQ":
                # Utiliser module-equalizer-sink de PulseAudio
                freq_map = {
                    'bass': '0',
                    'low_mid': '1',
                    'mid': '2', 
                    'high_mid': '3',
                    'treble': '4'
                }
                
                band_num = freq_map[band]
                cmd = ['pactl', 'set-sink-input-volume', '@DEFAULT_SINK@', f'{band_num}:{gain}']
                result = subprocess.run(cmd, capture_output=True, text=True)
                return result.returncode == 0
                
            elif self.equalizer_type == "ALSA_Equal":
                # Utiliser alsaequal
                cmd = ['alsaequal', f'set-band-{band}', str(gain)]
                result = subprocess.run(cmd, capture_output=True, text=True)
                return result.returncode == 0
                
        except Exception as e:
            print(f"Erreur Linux band {band}: {e}")
            return False
    
    def apply_preset(self, preset_name):
        """Applique un preset d'égalisation"""
        presets = {
            "Flat": {"bass": 0, "low_mid": 0, "mid": 0, "high_mid": 0, "treble": 0},
            "Rock": {"bass": 3, "low_mid": 1, "mid": -1, "high_mid": 2, "treble": 4},
            "Pop": {"bass": 2, "low_mid": 0, "mid": 1, "high_mid": 2, "treble": 3},
            "Jazz": {"bass": 1, "low_mid": 2, "mid": 1, "high_mid": 0, "treble": 2},
            "Classical": {"bass": 0, "low_mid": 1, "mid": 2, "high_mid": 1, "treble": 0},
            "Electronic": {"bass": 4, "low_mid": 2, "mid": 0, "high_mid": 3, "treble": 5},
            "Vocal": {"bass": -1, "low_mid": 1, "mid": 3, "high_mid": 2, "treble": 0},
            "Bass_Boost": {"bass": 6, "low_mid": 3, "mid": 0, "high_mid": 0, "treble": 0},
            "Treble_Boost": {"bass": 0, "low_mid": 0, "mid": 0, "high_mid": 3, "treble": 6}
        }
        
        if preset_name in presets:
            preset = presets[preset_name]
        elif preset_name in self.custom_presets:
            preset = self.custom_presets[preset_name]
        else:
            return False
        
        success = True
        for band, gain in preset.items():
            if not self.set_band(band, gain):
                success = False
        
        if success:
            self.current_preset = preset_name
            print(f"Preset '{preset_name}' appliqué avec succès")
        
        return success
    
    def get_current_settings(self):
        """Récupère les réglages actuels"""
        return {
            'bands': self.eq_bands.copy(),
            'enabled': self.is_enabled,
            'preset': self.current_preset,
            'platform': self.platform
        }
    
    def save_custom_preset(self, name, settings):
        """Sauvegarde un preset personnalisé"""
        self.custom_presets[name] = settings.copy()
        print(f"Preset personnalisé '{name}' sauvegardé")
        return True
    
    def toggle_equalizer(self):
        """Active/désactive l'égaliseur"""
        self.is_enabled = not self.is_enabled
        
        if self.is_enabled:
            # Réappliquer les réglages actuels
            for band, gain in self.eq_bands.items():
                self.set_band(band, gain)
        else:
            # Appliquer le preset Flat
            self.apply_preset("Flat")
        
        print(f"Égaliseur {'activé' if self.is_enabled else 'désactivé'}")
        return True

# Instance globale de l'égaliseur
equalizer = SystemAudioEqualizer()

class EqualizerHTTPHandler(BaseHTTPRequestHandler):
    """Serveur HTTP pour l'interface égaliseur"""
    
    def do_GET(self):
        """Gère les requêtes GET"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.serve_main_page()
        elif parsed_path.path == '/api/equalizer':
            self.handle_equalizer_api(parsed_path.query)
        elif parsed_path.path.endswith('.css'):
            self.serve_static_file('equalizer-styles.css', 'text/css')
        elif parsed_path.path.endswith('.js'):
            self.serve_static_file('equalizer.js', 'application/javascript')
        else:
            self.send_error(404)
    
    def serve_main_page(self):
        """Sert la page principale"""
        try:
            with open('equalizer.html', 'r', encoding='utf-8') as f:
                content = f.read()
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, "Page non trouvée")
    
    def serve_static_file(self, filename, content_type):
        """Sert les fichiers statiques"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            self.send_response(200)
            self.send_header('Content-type', content_type)
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404)
    
    def handle_equalizer_api(self, query):
        """Gère l'API de l'égaliseur"""
        params = parse_qs(query)
        action = params.get('action', [''])[0]
        
        response_data = {
            'success': False,
            'settings': equalizer.get_current_settings()
        }
        
        if action == 'get':
            response_data['success'] = True
            
        elif action == 'set_band':
            band = params.get('band', [''])[0]
            gain = float(params.get('gain', [0])[0])
            response_data['success'] = equalizer.set_band(band, gain)
            response_data['settings'] = equalizer.get_current_settings()
            
        elif action == 'apply_preset':
            preset = params.get('preset', [''])[0]
            response_data['success'] = equalizer.apply_preset(preset)
            response_data['settings'] = equalizer.get_current_settings()
            
        elif action == 'toggle':
            response_data['success'] = equalizer.toggle_equalizer()
            response_data['settings'] = equalizer.get_current_settings()
            
        elif action == 'save_preset':
            name = params.get('name', [''])[0]
            settings = equalizer.eq_bands.copy()
            response_data['success'] = equalizer.save_custom_preset(name, settings)
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response_data).encode('utf-8'))
    
    def log_message(self, format, *args):
        """Supprime les logs HTTP pour plus de clarté"""
        pass

def start_equalizer_server():
    """Démarre le serveur égaliseur"""
    server_address = ('localhost', 8081)
    httpd = HTTPServer(server_address, EqualizerHTTPHandler)
    
    print(f"🎚️ WaveCraft - Égaliseur Audio Système")
    print(f"📱 Plateforme: {PLATFORM}")
    print(f"🌐 Serveur démarré sur http://localhost:8081")
    print(f"🎵 Égaliseur: {'Activé' if equalizer.is_enabled else 'Désactivé'}")
    print(f"🎼 Preset actuel: {equalizer.current_preset}")
    print("\n💡 Ouvrez votre navigateur sur http://localhost:8081")
    print("⏹️  Appuyez sur Ctrl+C pour arrêter\n")
    
    # Ouvrir automatiquement le navigateur
    threading.Timer(1.0, lambda: webbrowser.open('http://localhost:8081')).start()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur égaliseur...")
        httpd.shutdown()

if __name__ == "__main__":
    start_equalizer_server()
