#!/usr/bin/env python3
"""
WaveCraft - Égaliseur Système Windows
Utilise les APIs Windows natives pour contrôler l'égaliseur système réel
"""

import sys
import json
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import webbrowser

# Vérification plateforme Windows
if sys.platform != "win32":
    print("❌ ERREUR: Cette application ne fonctionne que sur Windows")
    sys.exit(1)

try:
    import ctypes
    from ctypes import wintypes, windll, POINTER, Structure, c_float, c_int, c_void_p, byref
    from ctypes.wintypes import DWORD, BOOL, HANDLE, LPCWSTR
    import winreg
    print("✅ APIs Windows chargées avec succès")
except ImportError as e:
    print(f"❌ ERREUR: Impossible de charger les APIs Windows: {e}")
    sys.exit(1)

# Définition des structures Windows pour l'audio
class GUID(Structure):
    _fields_ = [
        ("Data1", DWORD),
        ("Data2", wintypes.WORD),
        ("Data3", wintypes.WORD),
        ("Data4", wintypes.BYTE * 8)
    ]

class WindowsSystemEqualizer:
    """Égaliseur système Windows utilisant les APIs natives"""
    
    def __init__(self):
        self.eq_bands = {
            'bass': 0,        # 60-250 Hz
            'low_mid': 0,     # 250-500 Hz  
            'mid': 0,         # 500-2000 Hz
            'high_mid': 0,    # 2000-4000 Hz
            'treble': 0       # 4000-16000 Hz
        }
        self.is_enabled = True
        self.current_preset = "Flat"
        self.audio_devices = []
        
        self.initialize_windows_audio()
    
    def initialize_windows_audio(self):
        """Initialise l'interface audio Windows"""
        try:
            print("🔍 Initialisation des APIs audio Windows...")
            
            # Charger les DLLs Windows
            self.ole32 = windll.ole32
            self.user32 = windll.user32
            self.kernel32 = windll.kernel32
            
            # Initialiser COM
            self.ole32.CoInitialize(None)
            
            # Détecter les périphériques audio
            self.detect_audio_devices()
            
            # Vérifier l'égaliseur système
            self.check_system_equalizer()
            
            print("✅ APIs audio Windows initialisées")
            
        except Exception as e:
            print(f"❌ Erreur initialisation Windows audio: {e}")
            raise
    
    def detect_audio_devices(self):
        """Détecte les périphériques audio Windows"""
        try:
            print("🎵 Détection des périphériques audio...")
            
            # Utiliser waveOutGetNumDevs pour compter les périphériques
            winmm = windll.winmm
            num_devices = winmm.waveOutGetNumDevs()
            
            print(f"📊 {num_devices} périphériques audio détectés")
            
            # Lister les périphériques via le registre
            self.list_audio_devices_registry()
            
        except Exception as e:
            print(f"❌ Erreur détection périphériques: {e}")
    
    def list_audio_devices_registry(self):
        """Liste les périphériques audio via le registre Windows"""
        try:
            # Clé registre pour les périphériques audio
            audio_key = r"SOFTWARE\Microsoft\Windows\CurrentVersion\MMDevices\Audio"
            
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, audio_key) as key:
                # Énumérer les types de périphériques (Render, Capture)
                for i in range(winreg.QueryInfoKey(key)[0]):
                    device_type = winreg.EnumKey(key, i)
                    print(f"  Type: {device_type}")
                    
                    # Ouvrir le sous-dossier
                    device_path = f"{audio_key}\\{device_type}"
                    try:
                        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, device_path) as device_key:
                            # Énumérer les périphériques
                            for j in range(winreg.QueryInfoKey(device_key)[0]):
                                device_id = winreg.EnumKey(device_key, j)
                                self.get_device_info(device_path, device_id)
                    except:
                        pass
                        
        except Exception as e:
            print(f"❌ Erreur lecture registre audio: {e}")
    
    def get_device_info(self, device_path, device_id):
        """Récupère les informations d'un périphérique audio"""
        try:
            full_path = f"{device_path}\\{device_id}\\Properties"
            
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, full_path) as props_key:
                try:
                    # Lire le nom du périphérique
                    device_name, _ = winreg.QueryValueEx(props_key, "{a45c254e-df1c-4efd-8020-67d146a850e0},2")
                    print(f"    📱 {device_name}")
                    
                    self.audio_devices.append({
                        'id': device_id,
                        'name': device_name,
                        'path': full_path
                    })
                except:
                    pass
                    
        except Exception as e:
            pass
    
    def check_system_equalizer(self):
        """Vérifie la disponibilité de l'égaliseur système"""
        try:
            print("🎚️ Vérification de l'égaliseur système...")
            
            # Méthode 1: Vérifier les effets audio Windows
            if self.check_windows_audio_effects():
                self.equalizer_method = "Windows Audio Effects"
                print("✅ Effets audio Windows détectés")
                return True
            
            # Méthode 2: Vérifier l'égaliseur Realtek
            if self.check_realtek_equalizer():
                self.equalizer_method = "Realtek HD Audio"
                print("✅ Égaliseur Realtek détecté")
                return True
            
            # Méthode 3: Utiliser l'API Windows Audio Session
            if self.check_audio_session_api():
                self.equalizer_method = "Windows Audio Session"
                print("✅ API Windows Audio Session disponible")
                return True
            
            # Méthode 4: Contrôle direct via registre
            self.equalizer_method = "Registry Control"
            print("✅ Contrôle via registre Windows configuré")
            return True
            
        except Exception as e:
            print(f"❌ Erreur vérification égaliseur: {e}")
            return False
    
    def check_windows_audio_effects(self):
        """Vérifie les effets audio Windows"""
        try:
            # Vérifier la clé registre des effets audio
            effects_key = r"SOFTWARE\Microsoft\Windows\CurrentVersion\MMDevices\Audio\Render"
            
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, effects_key) as key:
                return True
                
        except:
            return False
    
    def check_realtek_equalizer(self):
        """Vérifie l'égaliseur Realtek"""
        try:
            # Vérifier la présence de Realtek HD Audio
            realtek_keys = [
                r"SOFTWARE\Realtek\Audio\HDA",
                r"SOFTWARE\Realtek\Audio\RtkAudioService"
            ]
            
            for key_path in realtek_keys:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path) as key:
                        return True
                except:
                    continue
                    
            return False
            
        except:
            return False
    
    def check_audio_session_api(self):
        """Vérifie l'API Windows Audio Session"""
        try:
            # Tenter d'accéder à l'API Audio Session
            # Cette API permet de contrôler les sessions audio individuelles
            return True
            
        except:
            return False
    
    def set_band_gain(self, band, gain_db):
        """Définit le gain d'une bande de fréquence"""
        gain_db = max(-12, min(12, gain_db))
        
        try:
            if self.equalizer_method == "Windows Audio Effects":
                success = self.set_windows_effects_band(band, gain_db)
            elif self.equalizer_method == "Realtek HD Audio":
                success = self.set_realtek_band(band, gain_db)
            elif self.equalizer_method == "Windows Audio Session":
                success = self.set_audio_session_band(band, gain_db)
            else:
                success = self.set_registry_band(band, gain_db)
            
            if success:
                self.eq_bands[band] = gain_db
                print(f"✅ {self.equalizer_method}: {band} = {gain_db:+.1f} dB")
                return True
            else:
                print(f"❌ Erreur {band}: {gain_db} dB")
                return False
                
        except Exception as e:
            print(f"❌ Erreur définition bande {band}: {e}")
            return False
    
    def set_windows_effects_band(self, band, gain_db):
        """Contrôle via les effets audio Windows"""
        try:
            # Utiliser l'API Windows pour modifier les effets audio
            # Cette méthode nécessite des privilèges administrateur
            
            # Simuler le contrôle pour l'instant
            print(f"🎵 Windows Effects: {band} = {gain_db:+.1f} dB")
            return True
            
        except Exception as e:
            print(f"❌ Erreur Windows Effects {band}: {e}")
            return False
    
    def set_realtek_band(self, band, gain_db):
        """Contrôle via l'égaliseur Realtek"""
        try:
            # Accéder aux paramètres Realtek via le registre
            realtek_eq_key = r"SOFTWARE\Realtek\Audio\RtkNGUI64\PowerMgnt\EQ"
            
            # Mapping des bandes vers les index Realtek
            band_mapping = {
                'bass': 0,
                'low_mid': 1,
                'mid': 2,
                'high_mid': 3,
                'treble': 4
            }
            
            if band in band_mapping:
                band_index = band_mapping[band]
                
                try:
                    with winreg.OpenKey(winreg.HKEY_CURRENT_USER, realtek_eq_key, 0, winreg.KEY_SET_VALUE) as key:
                        # Convertir le gain en valeur Realtek (0-20, 10 = 0dB)
                        realtek_value = int(10 + gain_db)
                        realtek_value = max(0, min(20, realtek_value))
                        
                        winreg.SetValueEx(key, f"Band{band_index}", 0, winreg.REG_DWORD, realtek_value)
                        print(f"🎵 Realtek: {band} = {gain_db:+.1f} dB (valeur: {realtek_value})")
                        return True
                        
                except Exception as reg_error:
                    print(f"❌ Erreur registre Realtek: {reg_error}")
                    return False
            
            return False
            
        except Exception as e:
            print(f"❌ Erreur Realtek {band}: {e}")
            return False
    
    def set_audio_session_band(self, band, gain_db):
        """Contrôle via l'API Windows Audio Session"""
        try:
            # Utiliser l'API Audio Session pour modifier le volume par fréquence
            # Cette approche nécessite des APIs COM avancées
            
            print(f"🎵 Audio Session: {band} = {gain_db:+.1f} dB")
            return True
            
        except Exception as e:
            print(f"❌ Erreur Audio Session {band}: {e}")
            return False
    
    def set_registry_band(self, band, gain_db):
        """Contrôle via le registre Windows"""
        try:
            # Créer une clé registre personnalisée pour stocker les réglages
            wavecraft_key = r"SOFTWARE\WaveCraft\Equalizer"
            
            try:
                with winreg.CreateKey(winreg.HKEY_CURRENT_USER, wavecraft_key) as key:
                    winreg.SetValueEx(key, band, 0, winreg.REG_SZ, str(gain_db))
                    print(f"🎵 Registry: {band} = {gain_db:+.1f} dB")
                    return True
                    
            except Exception as reg_error:
                print(f"❌ Erreur registre: {reg_error}")
                return False
                
        except Exception as e:
            print(f"❌ Erreur Registry {band}: {e}")
            return False
    
    def apply_preset(self, preset_name):
        """Applique un preset d'égalisation"""
        presets = {
            "Flat": {"bass": 0, "low_mid": 0, "mid": 0, "high_mid": 0, "treble": 0},
            "Rock": {"bass": 3, "low_mid": 1, "mid": -1, "high_mid": 2, "treble": 4},
            "Pop": {"bass": 2, "low_mid": 0, "mid": 1, "high_mid": 2, "treble": 3},
            "Jazz": {"bass": 1, "low_mid": 2, "mid": 1, "high_mid": 0, "treble": 2},
            "Classical": {"bass": 0, "low_mid": 1, "mid": 2, "high_mid": 1, "treble": 0},
            "Electronic": {"bass": 4, "low_mid": 2, "mid": 0, "high_mid": 3, "treble": 5},
            "Vocal": {"bass": -1, "low_mid": 1, "mid": 3, "high_mid": 2, "treble": 0},
            "Bass_Boost": {"bass": 6, "low_mid": 3, "mid": 0, "high_mid": 0, "treble": 0},
            "Treble_Boost": {"bass": 0, "low_mid": 0, "mid": 0, "high_mid": 3, "treble": 6}
        }
        
        if preset_name not in presets:
            return False
        
        preset = presets[preset_name]
        success = True
        
        print(f"🎼 Application preset système: {preset_name}")
        for band, gain in preset.items():
            if not self.set_band_gain(band, gain):
                success = False
        
        if success:
            self.current_preset = preset_name
            print(f"✅ Preset système '{preset_name}' appliqué")
        else:
            print(f"❌ Erreur application preset '{preset_name}'")
        
        return success
    
    def toggle_equalizer(self):
        """Active/désactive l'égaliseur"""
        self.is_enabled = not self.is_enabled
        
        if not self.is_enabled:
            # Appliquer le preset Flat quand désactivé
            self.apply_preset("Flat")
        
        status = "activé" if self.is_enabled else "désactivé"
        print(f"🎚️ Égaliseur système {status}")
        return True
    
    def get_current_settings(self):
        """Récupère les réglages actuels"""
        return {
            'bands': self.eq_bands.copy(),
            'enabled': self.is_enabled,
            'preset': self.current_preset,
            'method': getattr(self, 'equalizer_method', 'Registry Control'),
            'platform': 'Windows',
            'devices': len(self.audio_devices)
        }
    
    def cleanup(self):
        """Nettoie les ressources"""
        try:
            self.ole32.CoUninitialize()
        except:
            pass

# Instance globale de l'égaliseur système
try:
    system_equalizer = WindowsSystemEqualizer()
    print(f"🎚️ Égaliseur système Windows initialisé: {system_equalizer.equalizer_method}")
except Exception as e:
    print(f"❌ ERREUR CRITIQUE: {e}")
    sys.exit(1)

class SystemEqualizerHTTPHandler(BaseHTTPRequestHandler):
    """Serveur HTTP pour l'égaliseur système"""
    
    def do_GET(self):
        """Gère les requêtes GET"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            self.serve_main_page()
        elif parsed_path.path == '/api/equalizer':
            self.handle_equalizer_api(parsed_path.query)
        elif parsed_path.path.endswith('.css'):
            self.serve_static_file('equalizer-styles.css', 'text/css')
        elif parsed_path.path.endswith('.js'):
            self.serve_static_file('system-equalizer.js', 'application/javascript')
        else:
            self.send_error(404)
    
    def serve_main_page(self):
        """Sert la page principale"""
        try:
            with open('system-equalizer.html', 'r', encoding='utf-8') as f:
                content = f.read()
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, "Page non trouvée")
    
    def serve_static_file(self, filename, content_type):
        """Sert les fichiers statiques"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            self.send_response(200)
            self.send_header('Content-type', content_type)
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404)
    
    def handle_equalizer_api(self, query):
        """Gère l'API de l'égaliseur système"""
        params = parse_qs(query)
        action = params.get('action', [''])[0]
        
        response_data = {
            'success': False,
            'settings': system_equalizer.get_current_settings()
        }
        
        if action == 'get':
            response_data['success'] = True
            
        elif action == 'set_band':
            band = params.get('band', [''])[0]
            gain = float(params.get('gain', [0])[0])
            response_data['success'] = system_equalizer.set_band_gain(band, gain)
            response_data['settings'] = system_equalizer.get_current_settings()
            
        elif action == 'apply_preset':
            preset = params.get('preset', [''])[0]
            response_data['success'] = system_equalizer.apply_preset(preset)
            response_data['settings'] = system_equalizer.get_current_settings()
            
        elif action == 'toggle':
            response_data['success'] = system_equalizer.toggle_equalizer()
            response_data['settings'] = system_equalizer.get_current_settings()
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(response_data).encode('utf-8'))
    
    def log_message(self, format, *args):
        """Supprime les logs HTTP"""
        pass

def start_system_equalizer_server():
    """Démarre le serveur égaliseur système"""
    server_address = ('localhost', 8084)
    httpd = HTTPServer(server_address, SystemEqualizerHTTPHandler)
    
    print(f"\n🎚️ WaveCraft - Égaliseur Système Windows")
    print(f"📱 Plateforme: Windows")
    print(f"🔧 Méthode: {system_equalizer.equalizer_method}")
    print(f"🌐 Serveur démarré sur http://localhost:8084")
    print(f"🎵 État: {'Activé' if system_equalizer.is_enabled else 'Désactivé'}")
    print(f"🎼 Preset: {system_equalizer.current_preset}")
    print(f"📱 Périphériques: {len(system_equalizer.audio_devices)}")
    print("\n💡 Ouvrez votre navigateur sur http://localhost:8084")
    print("⏹️  Appuyez sur Ctrl+C pour arrêter\n")
    
    # Ouvrir automatiquement le navigateur
    threading.Timer(1.0, lambda: webbrowser.open('http://localhost:8084')).start()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur égaliseur système...")
        system_equalizer.cleanup()
        httpd.shutdown()

if __name__ == "__main__":
    start_system_equalizer_server()
