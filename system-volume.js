// WaveCraft - Contrôle Volume Système JavaScript

class SystemVolumeController {
    constructor() {
        this.currentVolume = 0;
        this.isMuted = false;
        this.platform = 'Inconnu';
        this.apiBaseUrl = '/api/volume';
        this.isConnected = false;
        
        this.init();
    }

    async init() {
        this.setupElements();
        this.setupEventListeners();
        await this.connectToSystem();
        this.startPeriodicUpdate();
    }

    setupElements() {
        this.elements = {
            volumeBar: document.getElementById('volumeBar'),
            volumePercentage: document.getElementById('volumePercentage'),
            volumeSlider: document.getElementById('volumeSlider'),
            decreaseBtn: document.getElementById('decreaseBtn'),
            increaseBtn: document.getElementById('increaseBtn'),
            muteBtn: document.getElementById('muteBtn'),
            muteIcon: document.getElementById('muteIcon'),
            statusText: document.getElementById('statusText'),
            statusIndicator: document.getElementById('statusIndicator'),
            currentVolume: document.getElementById('currentVolume'),
            audioState: document.getElementById('audioState'),
            platform: document.getElementById('platform'),
            platformBadge: document.getElementById('platformBadge'),
            platformName: document.getElementById('platformName'),
            controlType: document.getElementById('controlType'),
            apiStatus: document.getElementById('apiStatus'),
            refreshBtn: document.getElementById('refreshBtn'),
            testSystemBtn: document.getElementById('testSystemBtn'),
            presetButtons: document.querySelectorAll('.preset-btn')
        };
    }

    setupEventListeners() {
        // Boutons de contrôle principal
        this.elements.decreaseBtn.addEventListener('click', () => this.decreaseVolume());
        this.elements.increaseBtn.addEventListener('click', () => this.increaseVolume());
        this.elements.muteBtn.addEventListener('click', () => this.toggleMute());
        
        // Slider de volume
        this.elements.volumeSlider.addEventListener('input', (e) => {
            this.setVolume(parseInt(e.target.value));
        });

        // Boutons de preset
        this.elements.presetButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const volume = parseInt(e.target.dataset.volume);
                this.setVolume(volume);
            });
        });

        // Boutons de test
        this.elements.refreshBtn.addEventListener('click', () => this.refreshSystemInfo());
        this.elements.testSystemBtn.addEventListener('click', () => this.testSystemVolume());

        // Raccourcis clavier
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowUp':
                    e.preventDefault();
                    this.increaseVolume();
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.decreaseVolume();
                    break;
                case ' ':
                    e.preventDefault();
                    this.toggleMute();
                    break;
                case 'r':
                case 'R':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        this.refreshSystemInfo();
                    }
                    break;
            }
        });
    }

    async connectToSystem() {
        try {
            this.setStatus('Connexion au système...', 'info');
            const response = await this.apiCall('get');
            
            if (response.success) {
                this.currentVolume = response.volume;
                this.isMuted = response.muted;
                this.platform = response.platform;
                this.isConnected = true;
                
                this.updateDisplay();
                this.setStatus('Connecté au système', 'success');
                this.elements.apiStatus.textContent = 'Connecté';
                this.elements.platformName.textContent = this.platform;
                
            } else {
                throw new Error('Échec de connexion');
            }
        } catch (error) {
            console.error('Erreur connexion système:', error);
            this.setStatus('Erreur de connexion', 'error');
            this.elements.apiStatus.textContent = 'Déconnecté';
            this.isConnected = false;
        }
    }

    async apiCall(action, params = {}) {
        const url = new URL(this.apiBaseUrl, window.location.origin);
        url.searchParams.append('action', action);
        
        Object.keys(params).forEach(key => {
            url.searchParams.append(key, params[key]);
        });

        const response = await fetch(url);
        return await response.json();
    }

    async setVolume(volume) {
        volume = Math.max(0, Math.min(100, volume));
        
        try {
            const response = await this.apiCall('set', { volume });
            
            if (response.success) {
                this.currentVolume = response.volume;
                this.isMuted = response.muted;
                this.updateDisplay();
                this.setStatus(`Volume défini à ${volume}%`, 'success');
                this.logVolumeChange(volume);
            } else {
                this.setStatus('Erreur définition volume', 'error');
            }
        } catch (error) {
            console.error('Erreur API setVolume:', error);
            this.setStatus('Erreur communication', 'error');
        }
    }

    async increaseVolume() {
        try {
            const response = await this.apiCall('increase');
            
            if (response.success) {
                this.currentVolume = response.volume;
                this.updateDisplay();
                this.setStatus('Volume augmenté', 'success');
            }
        } catch (error) {
            console.error('Erreur augmentation volume:', error);
            this.setStatus('Erreur augmentation', 'error');
        }
    }

    async decreaseVolume() {
        try {
            const response = await this.apiCall('decrease');
            
            if (response.success) {
                this.currentVolume = response.volume;
                this.updateDisplay();
                this.setStatus('Volume diminué', 'success');
            }
        } catch (error) {
            console.error('Erreur diminution volume:', error);
            this.setStatus('Erreur diminution', 'error');
        }
    }

    async toggleMute() {
        try {
            const response = await this.apiCall('mute');
            
            if (response.success) {
                this.isMuted = response.muted;
                this.updateDisplay();
                this.setStatus(this.isMuted ? 'Son coupé' : 'Son rétabli', 'success');
            }
        } catch (error) {
            console.error('Erreur muet:', error);
            this.setStatus('Erreur muet', 'error');
        }
    }

    async refreshSystemInfo() {
        this.setStatus('Actualisation...', 'info');
        await this.connectToSystem();
    }

    async testSystemVolume() {
        this.setStatus('Test du volume système...', 'info');
        
        const originalVolume = this.currentVolume;
        const testSequence = [30, 60, 90, 50, originalVolume];
        
        try {
            for (let i = 0; i < testSequence.length; i++) {
                await this.setVolume(testSequence[i]);
                await new Promise(resolve => setTimeout(resolve, 500));
                this.setStatus(`Test ${i + 1}/${testSequence.length} - ${testSequence[i]}%`, 'info');
            }
            
            this.setStatus('Test terminé avec succès', 'success');
        } catch (error) {
            console.error('Erreur test système:', error);
            this.setStatus('Erreur pendant le test', 'error');
            // Restaurer le volume original en cas d'erreur
            await this.setVolume(originalVolume);
        }
    }

    updateDisplay() {
        // Mettre à jour la barre de volume
        this.elements.volumeBar.style.width = `${this.currentVolume}%`;
        
        // Mettre à jour le pourcentage
        this.elements.volumePercentage.textContent = `${this.currentVolume}%`;
        this.elements.currentVolume.textContent = `${this.currentVolume}%`;
        
        // Mettre à jour le slider
        this.elements.volumeSlider.value = this.currentVolume;
        
        // Mettre à jour l'icône et l'état
        if (this.isMuted || this.currentVolume === 0) {
            this.elements.muteIcon.textContent = '🔇';
            this.elements.audioState.textContent = 'Muet';
            this.elements.muteBtn.classList.add('muted');
        } else {
            this.elements.muteBtn.classList.remove('muted');
            if (this.currentVolume < 30) {
                this.elements.muteIcon.textContent = '🔉';
                this.elements.audioState.textContent = 'Faible';
            } else if (this.currentVolume < 70) {
                this.elements.muteIcon.textContent = '🔊';
                this.elements.audioState.textContent = 'Moyen';
            } else {
                this.elements.muteIcon.textContent = '🔊';
                this.elements.audioState.textContent = 'Fort';
            }
        }
        
        // Mettre à jour les informations de plateforme
        this.elements.platform.textContent = this.platform;
        
        // Mettre à jour le type de contrôle
        if (this.platform === 'Simulation') {
            this.elements.controlType.textContent = 'Mode Simulation';
            this.elements.controlType.style.color = '#FF9800';
        } else {
            this.elements.controlType.textContent = 'Système Réel';
            this.elements.controlType.style.color = '#4CAF50';
        }
    }

    setStatus(message, type = 'info') {
        this.elements.statusText.textContent = message;
        
        const statusDot = this.elements.statusIndicator.querySelector('.status-dot');
        statusDot.className = 'status-dot';
        
        switch(type) {
            case 'success':
                statusDot.style.background = '#4CAF50';
                break;
            case 'warning':
                statusDot.style.background = '#FF9800';
                break;
            case 'error':
                statusDot.style.background = '#F44336';
                break;
            default:
                statusDot.style.background = '#2196F3';
        }
        
        // Effacer le message après 3 secondes (sauf pour les erreurs)
        if (type !== 'error') {
            setTimeout(() => {
                this.elements.statusText.textContent = 'Prêt';
                statusDot.style.background = '#4CAF50';
            }, 3000);
        }
    }

    logVolumeChange(volume) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] Volume système changé à ${volume}%`);
    }

    startPeriodicUpdate() {
        // Actualiser les informations toutes les 5 secondes
        setInterval(async () => {
            if (this.isConnected) {
                try {
                    const response = await this.apiCall('get');
                    if (response.success) {
                        // Mettre à jour silencieusement si le volume a changé
                        if (response.volume !== this.currentVolume || response.muted !== this.isMuted) {
                            this.currentVolume = response.volume;
                            this.isMuted = response.muted;
                            this.updateDisplay();
                        }
                    }
                } catch (error) {
                    // Ignorer les erreurs de mise à jour périodique
                }
            }
        }, 5000);
    }
}

// Initialiser le contrôleur quand la page est chargée
document.addEventListener('DOMContentLoaded', () => {
    window.systemVolumeController = new SystemVolumeController();
});

// Gestion de la visibilité de la page
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.systemVolumeController) {
        window.systemVolumeController.refreshSystemInfo();
    }
});
