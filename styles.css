/* WaveCraft - Styles pour Contrôle de Volume */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.volume-control {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    flex: 1;
}

.volume-display {
    text-align: center;
    margin-bottom: 30px;
}

.volume-meter {
    width: 100%;
    height: 20px;
    background: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
    position: relative;
}

.volume-bar {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #FFC107, #FF5722);
    border-radius: 10px;
    width: 50%;
    transition: width 0.3s ease;
}

.volume-percentage {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

.controls {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
    gap: 10px;
}

.control-btn {
    background: linear-gradient(145deg, #f0f0f0, #d0d0d0);
    border: none;
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 5px 5px 10px rgba(0,0,0,0.1);
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 7px 7px 15px rgba(0,0,0,0.15);
}

.control-btn:active {
    transform: translateY(0);
    box-shadow: 3px 3px 8px rgba(0,0,0,0.1);
}

.control-btn .icon {
    font-size: 1.5rem;
}

.control-btn .text {
    font-size: 0.9rem;
    font-weight: 500;
}

.decrease {
    background: linear-gradient(145deg, #ffebee, #ffcdd2);
}

.increase {
    background: linear-gradient(145deg, #e8f5e8, #c8e6c9);
}

.mute {
    background: linear-gradient(145deg, #fff3e0, #ffe0b2);
}

.volume-slider-container {
    margin-bottom: 30px;
}

.volume-slider {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: #e0e0e0;
    outline: none;
    cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.volume-slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.status {
    text-align: center;
    margin-bottom: 20px;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: #f5f5f5;
    border-radius: 20px;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #4CAF50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.info-panel {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.info-panel h3 {
    margin-bottom: 15px;
    color: #333;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.label {
    font-weight: 500;
    color: #666;
}

.value {
    font-weight: bold;
    color: #333;
}

footer {
    text-align: center;
    color: white;
    opacity: 0.8;
    margin-top: 20px;
}

/* Responsive Design */
@media (max-width: 480px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .volume-control {
        padding: 20px;
    }
    
    .controls {
        flex-direction: column;
        gap: 15px;
    }
    
    .control-btn {
        flex-direction: row;
        justify-content: center;
        padding: 15px;
    }
    
    .control-btn .icon {
        margin-right: 10px;
    }
}
