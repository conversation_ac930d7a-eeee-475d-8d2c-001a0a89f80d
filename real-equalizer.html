<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WaveCraft - Égaliseur Audio RÉEL</title>
    <link rel="stylesheet" href="equalizer-styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎚️ WaveCraft</h1>
            <p>Égaliseur Audio Système RÉEL</p>
            <div class="platform-badge" id="platformBadge">
                <span id="platformName">Windows</span>
                <span class="method-badge" id="methodBadge">Détection...</span>
            </div>
        </header>

        <main class="equalizer-control">
            <div class="equalizer-header">
                <div class="eq-status">
                    <button class="power-btn" id="powerBtn">
                        <span class="power-icon" id="powerIcon">⚡</span>
                        <span class="power-text" id="powerText">RÉEL</span>
                    </button>
                    <div class="current-preset">
                        <span class="preset-label">Preset:</span>
                        <span class="preset-name" id="currentPreset">Flat</span>
                    </div>
                </div>
                
                <div class="real-indicator">
                    <div class="real-badge">
                        <span class="real-icon">✅</span>
                        <span class="real-text">CONTRÔLE SYSTÈME RÉEL</span>
                    </div>
                </div>
            </div>

            <div class="equalizer-bands">
                <div class="band-container">
                    <div class="band-control" data-band="bass">
                        <div class="band-label">Basses</div>
                        <div class="band-freq">60-250Hz</div>
                        <div class="band-slider-container">
                            <input type="range" class="band-slider" 
                                   min="-12" max="12" value="0" step="0.5"
                                   id="bassSlider">
                            <div class="slider-marks">
                                <span>-12</span>
                                <span>0</span>
                                <span>+12</span>
                            </div>
                        </div>
                        <div class="band-value" id="bassValue">0 dB</div>
                    </div>

                    <div class="band-control" data-band="low_mid">
                        <div class="band-label">Médiums-Bas</div>
                        <div class="band-freq">250-500Hz</div>
                        <div class="band-slider-container">
                            <input type="range" class="band-slider" 
                                   min="-12" max="12" value="0" step="0.5"
                                   id="lowMidSlider">
                            <div class="slider-marks">
                                <span>-12</span>
                                <span>0</span>
                                <span>+12</span>
                            </div>
                        </div>
                        <div class="band-value" id="lowMidValue">0 dB</div>
                    </div>

                    <div class="band-control" data-band="mid">
                        <div class="band-label">Médiums</div>
                        <div class="band-freq">500-2kHz</div>
                        <div class="band-slider-container">
                            <input type="range" class="band-slider" 
                                   min="-12" max="12" value="0" step="0.5"
                                   id="midSlider">
                            <div class="slider-marks">
                                <span>-12</span>
                                <span>0</span>
                                <span>+12</span>
                            </div>
                        </div>
                        <div class="band-value" id="midValue">0 dB</div>
                    </div>

                    <div class="band-control" data-band="high_mid">
                        <div class="band-label">Médiums-Hauts</div>
                        <div class="band-freq">2-4kHz</div>
                        <div class="band-slider-container">
                            <input type="range" class="band-slider" 
                                   min="-12" max="12" value="0" step="0.5"
                                   id="highMidSlider">
                            <div class="slider-marks">
                                <span>-12</span>
                                <span>0</span>
                                <span>+12</span>
                            </div>
                        </div>
                        <div class="band-value" id="highMidValue">0 dB</div>
                    </div>

                    <div class="band-control" data-band="treble">
                        <div class="band-label">Aigus</div>
                        <div class="band-freq">4-16kHz</div>
                        <div class="band-slider-container">
                            <input type="range" class="band-slider" 
                                   min="-12" max="12" value="0" step="0.5"
                                   id="trebleSlider">
                            <div class="slider-marks">
                                <span>-12</span>
                                <span>0</span>
                                <span>+12</span>
                            </div>
                        </div>
                        <div class="band-value" id="trebleValue">0 dB</div>
                    </div>
                </div>
            </div>

            <div class="presets-section">
                <h3>🎼 Presets d'Égalisation RÉELS</h3>
                <div class="preset-buttons">
                    <button class="preset-btn active" data-preset="Flat">Flat</button>
                    <button class="preset-btn" data-preset="Rock">Rock</button>
                    <button class="preset-btn" data-preset="Pop">Pop</button>
                    <button class="preset-btn" data-preset="Jazz">Jazz</button>
                    <button class="preset-btn" data-preset="Classical">Classique</button>
                    <button class="preset-btn" data-preset="Electronic">Électro</button>
                    <button class="preset-btn" data-preset="Vocal">Vocal</button>
                    <button class="preset-btn" data-preset="Bass_Boost">Bass+</button>
                    <button class="preset-btn" data-preset="Treble_Boost">Aigus+</button>
                </div>
            </div>

            <div class="status">
                <div class="status-indicator" id="statusIndicator">
                    <span class="status-dot"></span>
                    <span class="status-text" id="statusText">Connexion...</span>
                </div>
            </div>

            <div class="info-panel">
                <h3>📊 Informations Égaliseur RÉEL</h3>
                <div class="info-item">
                    <span class="label">État :</span>
                    <span class="value" id="eqState">Détection...</span>
                </div>
                <div class="info-item">
                    <span class="label">Méthode :</span>
                    <span class="value" id="equalizerMethod">Détection...</span>
                </div>
                <div class="info-item">
                    <span class="label">Preset actuel :</span>
                    <span class="value" id="currentPresetInfo">Flat</span>
                </div>
                <div class="info-item">
                    <span class="label">Type :</span>
                    <span class="value real-control">SYSTÈME RÉEL</span>
                </div>
            </div>

            <div class="test-controls">
                <button class="test-btn" id="refreshBtn">
                    <span class="icon">🔄</span>
                    <span class="text">Actualiser</span>
                </button>
                <button class="test-btn" id="resetBtn">
                    <span class="icon">🔄</span>
                    <span class="text">Reset</span>
                </button>
                <button class="test-btn real-test" id="testEqBtn">
                    <span class="icon">🧪</span>
                    <span class="text">Test RÉEL</span>
                </button>
            </div>

            <div class="warning-panel">
                <h3>⚠️ Avertissement</h3>
                <p>Cet égaliseur modifie VRAIMENT l'audio de votre système. Les changements affectent tous les sons de votre PC.</p>
                <p>Si vous n'entendez aucune différence, vérifiez que :</p>
                <ul>
                    <li>✅ EqualizerAPO est installé et configuré</li>
                    <li>✅ Votre périphérique audio est sélectionné dans EqualizerAPO</li>
                    <li>✅ Vous écoutez de la musique pendant les tests</li>
                </ul>
            </div>
        </main>

        <footer>
            <p>WaveCraft v3.0 - Égaliseur Audio Système RÉEL</p>
            <p class="api-status">API: <span id="apiStatus">Connexion...</span></p>
        </footer>
    </div>

    <script src="real-equalizer.js"></script>
</body>
</html>
